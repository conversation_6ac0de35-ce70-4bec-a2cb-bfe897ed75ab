<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class ProcessTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    public string $taskId;
    public string $taskType;
    
    public function __construct(string $taskId, string $taskType)
    {
        $this->taskId = $taskId;
        $this->taskType = $taskType;
    }
    
    public function handle(): void
    {
        try {
            $this->updateProgress(5, 'processing', 'Starting task processing...');
            
            // Simulate different task types
            switch ($this->taskType) {
                case 'file_upload':
                    $this->processFileUpload();
                    break;
                case 'data_processing':
                    $this->processData();
                    break;
                case 'report_generation':
                    $this->generateReport();
                    break;
            }
            
            $this->updateProgress(100, 'completed', 'Task completed successfully!');
            
        } catch (\Exception $e) {
            $this->updateProgress(0, 'failed', 'Task failed: ' . $e->getMessage());
        }
    }
    
    private function processFileUpload(): void
    {
        for ($i = 10; $i <= 100; $i += 10) {
            $this->updateProgress($i, 'processing', "Uploading file... {$i}%");
            sleep(1); // Simulate work
        }
    }
    
    private function processData(): void
    {
        $steps = [
            'Validating data...',
            'Processing records...',
            'Generating insights...',
            'Saving results...'
        ];
        
        foreach ($steps as $index => $step) {
            $progress = 25 + ($index * 20);
            $this->updateProgress($progress, 'processing', $step);
            sleep(2); // Simulate work
        }
    }
    
    private function generateReport(): void
    {
        $steps = [
            'Collecting data...',
            'Analyzing trends...',
            'Creating visualizations...',
            'Generating PDF...',
            'Finalizing report...'
        ];
        
        foreach ($steps as $index => $step) {
            $progress = 15 + ($index * 17);
            $this->updateProgress($progress, 'processing', $step);
            sleep(1.5); // Simulate work
        }
    }
    
    private function updateProgress(int $progress, string $status, string $message): void
    {
        // Make internal API call to update progress
        Http::post(route('progress.update', $this->taskId), [
            'progress' => $progress,
            'status' => $status,
            'message' => $message,
            'current_step' => $message,
            'estimated_completion' => $progress < 100 ? now()->addSeconds((100 - $progress) * 2)->toISOString() : null
        ]);
    }
}
