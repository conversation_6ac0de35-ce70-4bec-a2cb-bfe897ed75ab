<?php

namespace App\Models;

use App\Account;
use App\Mapping;
use App\Traits\ModelExportable;
use App\Traits\ModelImportable;
use App\Traits\SendMail;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class LinkedPharmacy extends Model
{
    use SoftDeletes;
    use ModelImportable;
    use SendMail;
    use ModelExportable;

    protected $guard_name = 'api';

    protected $table = 'linked_pharmacies';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'account_id',
        'user_id',
        'pharmable_id',
        'pharmable_type',
        'ratio',
        'file_id'
    ];

    public function pharmable()
    {
        return $this->morphTo();
    }
    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function mapping()
    {
        return $this->belongsTo(Mapping::class, 'pharmable_id')
                    ->where('pharmable_type', Mapping::class);
    }
}
