<?php

namespace App\Models;

use App\Account;
use App\AccountLines;
use App\Classes;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\PlanVisitDetails;
use App\Services\ListService;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class NewAccountDoctor extends Model
{
    use SoftDeletes;

    protected $guard_name = 'api';

    protected $table = 'new_account_doctors';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'account_id',
        'doctor_id',
        'line_id',
        'account_lines_id',
        'from_date',
        'to_date',
        'file_id',
        'class_id'
    ];

    protected $casts = [
        'from_date' => 'datetime',
        'to_date' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    public function logActivities()
    {
        return $this->morphMany('App\LogActivity', 'loggable');
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class, 'doctor_id');
    }

    public function class()
    {
        return $this->belongsTo(Classes::class, 'class_id');
    }
    public function details()
    {
        return $this->morphOne(PlanVisitDetails::class, 'visitable');
    }
    public function accountLine()
    {
        return $this->belongsTo(AccountLines::class, 'account_lines_id');
    }
    public function user()
    {
        $line = Line::find($this->line_id);
        $account = (new ListService())->getActiveList(null, null, [$line->id], [], [], [], [], [$this->id])->first();
        return LineDivision::find($account->div_id)?->linedivuser?->first()->user;
    }
}
