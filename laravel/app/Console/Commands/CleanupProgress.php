<?php

namespace App\Console\Commands;

use App\Services\ProgressService;
use Illuminate\Console\Command;

class CleanupProgress extends Command
{
    protected $signature = 'progress:cleanup';
    protected $description = 'Clean up expired progress data and stale streams';
    
    public function handle(ProgressService $progressService)
    {
        $this->info('Starting progress cleanup...');
        
        $progressService->cleanup();
        
        $stats = $progressService->getStatistics();
        $this->info('Cleanup completed!');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Active Progress Tasks', $stats['active_progress_tasks']],
                ['Active Streams', $stats['active_streams']],
                ['Memory Usage', number_format($stats['memory_usage'] / 1024 / 1024, 2) . ' MB'],
                ['Redis Memory', $stats['redis_memory']]
            ]
        );
    }
}
