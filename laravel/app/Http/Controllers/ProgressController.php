<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Str;

class ProgressController extends Controller
{
    private const PROGRESS_PREFIX = 'progress:';
    private const ACTIVE_STREAMS_PREFIX = 'active_streams:';
    private const STREAM_TIMEOUT = 300; // 5 minutes
    
    /**
     * Start a new task and return task ID
     */
    public function startTask(Request $request): JsonResponse
    {
        $request->validate([
            'task_type' => 'required|string|in:file_upload,data_processing,report_generation',
            'user_id' => 'required|integer'
        ]);
        
        $taskId = $this->generateTaskId($request->user_id);
        
        // Initialize progress data
        $this->setProgress($taskId, [
            'progress' => 0,
            'status' => 'starting',
            'message' => 'Task initialization...',
            'user_id' => $request->user_id,
            'task_type' => $request->task_type,
            'started_at' => now()->toISOString(),
            'estimated_completion' => null,
            'current_step' => 'initializing',
            'total_steps' => 100
        ]);
        
        // Dispatch the actual job
        dispatch(new \App\Jobs\ProcessTaskJob($taskId, $request->task_type));
        
        return response()->json([
            'task_id' => $taskId,
            'stream_url' => route('progress.stream', $taskId)
        ]);
    }
    
    /**
     * Stream progress updates via SSE
     */
    public function streamProgress(string $taskId): StreamedResponse
    {
        // Validate task exists and get initial data
        $progressData = $this->getProgress($taskId);
        if (!$progressData) {
            return response()->stream(function () {
                echo "data: " . json_encode(['error' => 'Task not found']) . "\n\n";
            }, 404);
        }
        
        // Register this stream as active
        $streamId = $this->registerActiveStream($taskId);
        
        return response()->stream(function () use ($taskId, $streamId) {
            $this->handleProgressStream($taskId, $streamId);
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Headers' => 'Cache-Control',
            'X-Accel-Buffering' => 'no' // Disable Nginx buffering
        ]);
    }
    
    /**
     * Handle the actual streaming logic
     */
    private function handleProgressStream(string $taskId, string $streamId): void
    {
        $lastUpdate = null;
        $heartbeatCounter = 0;
        
        while (true) {
            // Check if client disconnected
            if (connection_aborted()) {
                $this->cleanupStream($taskId, $streamId);
                break;
            }
            
            // Get current progress
            $currentProgress = $this->getProgress($taskId);
            
            if (!$currentProgress) {
                // Task was cleaned up or expired
                echo "data: " . json_encode(['error' => 'Task expired']) . "\n\n";
                break;
            }
            
            // Send update only if data changed
            if ($currentProgress !== $lastUpdate) {
                echo "data: " . json_encode($currentProgress) . "\n\n";
                $lastUpdate = $currentProgress;
            } else {
                // Send heartbeat every 30 seconds to keep connection alive
                $heartbeatCounter++;
                if ($heartbeatCounter >= 30) {
                    echo "event: heartbeat\n";
                    echo "data: " . json_encode(['timestamp' => time()]) . "\n\n";
                    $heartbeatCounter = 0;
                }
            }
            
            // Check if task is completed
            if ($currentProgress['status'] === 'completed' || $currentProgress['status'] === 'failed') {
                // Send final update and close
                echo "event: complete\n";
                echo "data: " . json_encode($currentProgress) . "\n\n";
                $this->cleanupStream($taskId, $streamId);
                break;
            }
            
            // Update stream activity timestamp
            $this->updateStreamActivity($taskId, $streamId);
            
            ob_flush();
            flush();
            sleep(1);
        }
    }
    
    /**
     * Update progress from job/process (thread-safe)
     */
    public function updateProgress(Request $request, string $taskId): JsonResponse
    {
        $request->validate([
            'progress' => 'required|integer|min:0|max:100',
            'status' => 'required|string|in:processing,completed,failed,paused',
            'message' => 'required|string|max:255',
            'current_step' => 'nullable|string',
            'estimated_completion' => 'nullable|date'
        ]);
        
        $updateData = [
            'progress' => $request->progress,
            'status' => $request->status,
            'message' => $request->message,
            'current_step' => $request->current_step ?? 'processing',
            'estimated_completion' => $request->estimated_completion,
            'updated_at' => now()->toISOString()
        ];
        
        // Use Redis transactions for atomic updates
        $this->atomicUpdateProgress($taskId, $updateData);
        
        return response()->json(['success' => true]);
    }
    
    /**
     * Generate unique task ID
     */
    private function generateTaskId(int $userId): string
    {
        return $userId . '_' . Str::uuid()->toString();
    }
    
    /**
     * Set progress data with expiration
     */
    private function setProgress(string $taskId, array $data): void
    {
        $key = self::PROGRESS_PREFIX . $taskId;
        Redis::setex($key, self::STREAM_TIMEOUT, json_encode($data));
    }
    
    /**
     * Get progress data
     */
    private function getProgress(string $taskId): ?array
    {
        $key = self::PROGRESS_PREFIX . $taskId;
        $data = Redis::get($key);
        return $data ? json_decode($data, true) : null;
    }
    
    /**
     * Atomically update progress data
     */
    private function atomicUpdateProgress(string $taskId, array $updateData): void
    {
        $key = self::PROGRESS_PREFIX . $taskId;
        
        Redis::transaction(function ($redis) use ($key, $updateData) {
            $current = $redis->get($key);
            if ($current) {
                $currentData = json_decode($current, true);
                $mergedData = array_merge($currentData, $updateData);
                $redis->setex($key, self::STREAM_TIMEOUT, json_encode($mergedData));
            }
        });
    }
    
    /**
     * Register active stream
     */
    private function registerActiveStream(string $taskId): string
    {
        $streamId = Str::uuid()->toString();
        $key = self::ACTIVE_STREAMS_PREFIX . $taskId;
        
        Redis::hset($key, $streamId, time());
        Redis::expire($key, self::STREAM_TIMEOUT);
        
        return $streamId;
    }
    
    /**
     * Update stream activity
     */
    private function updateStreamActivity(string $taskId, string $streamId): void
    {
        $key = self::ACTIVE_STREAMS_PREFIX . $taskId;
        Redis::hset($key, $streamId, time());
    }
    
    /**
     * Cleanup stream
     */
    private function cleanupStream(string $taskId, string $streamId): void
    {
        $key = self::ACTIVE_STREAMS_PREFIX . $taskId;
        Redis::hdel($key, $streamId);
        
        // If no more active streams, optionally clean up progress data
        if (Redis::hlen($key) === 0) {
            Redis::del($key);
        }
    }
    
    /**
     * Get task status (for polling fallback)
     */
    public function getTaskStatus(string $taskId): JsonResponse
    {
        $progress = $this->getProgress($taskId);
        
        if (!$progress) {
            return response()->json(['error' => 'Task not found'], 404);
        }
        
        return response()->json($progress);
    }
    
    /**
     * Cancel task
     */
    public function cancelTask(string $taskId): JsonResponse
    {
        $this->atomicUpdateProgress($taskId, [
            'status' => 'cancelled',
            'message' => 'Task cancelled by user',
            'progress' => 0
        ]);
        
        return response()->json(['success' => true]);
    }
}
