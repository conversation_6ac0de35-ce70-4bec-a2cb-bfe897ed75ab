<?php

namespace App\Http\Controllers;

use App\Account;
use App\Brand;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\CommercialRequestRequest;
use App\Http\Requests\ImportRequest;
use App\Line;
use App\LineDivision;
use App\LineProduct;
use App\LineUserPosition;
use App\LinkedParmaciesSetting;
use App\Mapping;
use App\MappingUnifiedCode;
use App\Models\AccountDoctorPharmacy;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\Attachment;
use App\Models\Budget;
use App\Models\BudgetSetting;
use App\Models\CommercialPharmacySetting;
use App\Models\CommercialRequest\CommercialAgenda;
use App\Models\CommercialRequest\CommercialCategoriesCost;
use App\Models\CommercialRequest\CommercialCostType;
use App\Models\CommercialRequest\CommercialDivision;
use App\Models\CommercialRequest\CommercialDoctor;
use App\Models\CommercialRequest\CommercialDoctorCostType;
use App\Models\CommercialRequest\CommercialLine;
use App\Models\CommercialRequest\CommercialOutOfList;
use App\Models\CommercialRequest\CommercialOutOfListCostType;
use App\Models\CommercialRequest\CommercialPharmacy;
use App\Models\CommercialRequest\CommercialProduct;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\CommercialRequest\CommercialSetting;
use App\Models\CommercialRequest\CommercialUser;
use App\Models\CommercialRequest\CommercialUserCostType;
use App\PlanVisitDetails;
use App\Models\CommercialRequest\Costs\CostType;
use App\Models\CommercialRequest\EndCommercialPayment;
use App\Models\CommercialRequest\PartialPayment;
use App\Models\CommercialRequest\ServiceComplete;
use App\Models\LinkedPharmacy;
use App\Models\ListType;
use App\Models\NewAccountDoctor;
use App\Models\PaidRequest;
use App\Models\Policy;
use App\Models\RequestFeedback;
use App\Notifications\CommercialCreatedNotification;
use App\Product;
use App\Sale;
use App\Services\ListService;
use App\Setting;
use App\UserPosition;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CommercialRequestController extends ApiController
{


    private array $before = [];
    private array $after = [];
    private array $month_all = [];
    private ?Carbon $now;
    public function index()
    {
        /**@var User $user */
        $user = Auth::user();
        $commercial_requests = DB::table('commercial_requests')->select(
            'commercial_requests.id as id',
            'commercial_requests.ll as lat',
            DB::raw("
            CASE
                WHEN crm_commercial_requests.archived = 1 THEN 'Archived'
                ELSE ''
            END as archived
        "),
            'commercial_requests.lg as lng',
            'commercial_requests.from_date as from',
            DB::raw('DATE_FORMAT(crm_commercial_requests.created_at,"%Y-%m-%d") as insertion'),
            'commercial_requests.to_date as to',
            'request_types.name as type',
            'users.fullname as user',
            'plan_visit_details.approval as status',
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('IFNULL(group_concat(distinct crm_division_types.color),"") as color'),
            DB::raw('IFNULL(group_concat(distinct crm_commercial_requests.description),"") as description'),
            DB::raw('IFNULL(crm_commercial_requests.amount,"") as total'),
            DB::raw('IFNULL(group_concat(distinct crm_paid_requests.type),"") as paid'),
            DB::raw('IFNULL(group_concat(distinct crm_request_feedback.feedback),"") as feedback'),
            DB::raw('IFNULL(sum(crm_commercial_requests.amount),"") as total_amount'),
        )
            ->leftJoin('users', 'commercial_requests.user_id', 'users.id')
            ->leftJoin('commercial_lines', 'commercial_requests.id', 'commercial_lines.request_id')
            ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->leftJoin('lines', 'commercial_lines.line_id', 'lines.id')
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.request_id')
            ->leftJoin('line_divisions', 'commercial_divisions.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', CommercialRequest::class);
                }
            )
            ->leftJoin(
                'request_feedback',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'request_feedback.requestable_id');
                    $join->where('request_feedback.requestable_type', CommercialRequest::class);
                }
            )
            ->leftJoin(
                'paid_requests',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'paid_requests.paidable_id');
                    $join->where('paid_requests.paidable_type', CommercialRequest::class);
                }
            )
            ->orderBy('id', 'DESC')
            ->whereNull('commercial_requests.deleted_at');
        $users = $user->indexPerUser($user);
        if (count($users) > 0) {
            $commercial_requests = $commercial_requests->whereIntegerInRaw('commercial_requests.user_id', $users->values());
        }
        if (
            request('query') == "Approved" || request('query') == "approved"
        ) {
            $commercial_requests = $commercial_requests->where('plan_visit_details.approval', 1);
        } elseif (
            request('query') == "Disapproved" || request('query') == "disapproved"
        ) {
            $commercial_requests = $commercial_requests->where('plan_visit_details.approval', 0);
        } elseif (
            request('query') == "Pending" || request('query') == "pending"
        ) {
            $commercial_requests = $commercial_requests->whereNull('plan_visit_details.approval');
        } else {
            $commercial_requests = $commercial_requests
                ->where(fn($q) => $q->where('commercial_requests.id', 'Like', '%' . request('query') . '%')
                    ->orWhere('users.fullname', 'Like', '%' . request('query') . '%')
                    // ->orWhere('commercial_requests.from_date', 'Like', '%' . request('query') . '%')
                    // ->orWhere('commercial_requests.to_date', 'Like', '%' . request('query') . '%')
                    ->orWhere('commercial_requests.created_at', 'Like', '%' . request('query') . '%')
                    ->orWhere('commercial_requests.description', 'Like', '%' . request('query') . '%')
                    ->orWhere('request_feedback.feedback', 'Like', '%' . request('query') . '%')
                    ->orWhere('paid_requests.type', 'Like', '%' . request('query') . '%')
                    ->orWhere('request_types.name', 'Like', '%' . request('query') . '%'));
        }

        if (request('columnFilter')) {


            foreach (request('columnFilter') as $key => $value) {
                $newKey = match ($key) {
                    'id' => 'commercial_requests.id',
                    'user' => 'users.fullname',
                    'type' => 'request_types.name',
                    'from' => 'commercial_requests.from_date',
                    'to' => 'commercial_requests.to_date',
                    'status' => 'plan_visit_details.approval',
                    default => null,
                };
                if ($newKey != null) {
                    $commercial_requests->where($newKey, "like", '%' . $value . '%');
                }
            }
        }

        LogActivity::addLog();
        return $this->respond($commercial_requests->orderBy('id', 'DESC')->groupBy("id", "plan_visit_details.approval")->simplePaginate(300));
    }

    public function sumAmount(Request $request)
    {
        $totalAmount = $request->sum('amount');
        return $this->respond($totalAmount);
    }

    private function getStatus($commercial)
    {
        if ($commercial->details?->approval === null) return null;
        if ($commercial->details?->approval === 1) return 1;
        if ($commercial->details?->approval === 0) return 0;
    }

    public function commercialFlow(Request $request)
    {
        $commercial = CommercialRequest::find($request->id);
        $approvalFlow = $commercial->details?->approvalFlows()->count();
        $data = collect([]);
        if ($approvalFlow > 0) {
            $data = $data->merge($commercial->details?->approvalFlows()->get());
        } else {
            $data = $data->push($commercial->details);
        }
        $data = $data->map(function ($detail) use ($commercial) {
            return [
                'approved_by' => $detail?->user?->fullname ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'status' => $detail?->approval ?? null,
                'comment' => $detail?->description ?? '',
                'reason' => $detail?->approval == 0 ? $commercial->reasons->first()?->reason : "",
                'date' => is_null($detail?->approval) ? "" : Carbon::parse($detail?->updated_at)->toDateTimeString(),
            ];
        });
        return $this->respond($data);
    }

    public function getCommercialEmployee(Request $request)
    {
        $commercialUsers = CommercialUser::where('request_id', $request->id)->get()->map(function ($commercialUser) use ($request) {
            $data = $this->getEmployeeCosts($commercialUser, $request);
            return [
                'id' => $commercialUser->user?->id ?? '',
                'user' => $commercialUser->user?->fullname ?? '',
                'cost' =>  $data['cost_name'] ?? '',
                'amount' => $data['amount'],

            ];
        });
        return $this->respond($commercialUsers);
    }
    private function getEmployeeCosts($commercialUser, $request)
    {
        $costType = collect([]);
        $commercialUser->userCosts()
            ->where('request_id', $commercialUser->request_id)->with('costType')->get()->each(function ($userCost) use ($costType) {
                $costType = $costType->push($userCost->costType);
            });
        $amount = CommercialCostType::where('request_id', $request->id)
            ->whereIntegerInRaw('cost_type_id', $costType->pluck('id'))
            ->sum('amount');
        return array('cost_name' => $costType->pluck('name')->implode(' , '), 'amount' => $amount != 0 ? $amount : '');
    }
    public function getCommercialDoctor(Request $request)
    {
        $commercialDoctors = CommercialDoctor::where('request_id', $request->id)->get()->map(function ($commercialDoctor) use ($request) {
            $data = $this->getDoctorCosts($commercialDoctor, $request);
            return [
                'doctor_id' => $commercialDoctor->doctor_id ?? '',
                'doctor' => $commercialDoctor->doctor?->name ?? '',
                'account_id' => $commercialDoctor->account_id ?? '',
                'account' => $commercialDoctor->account?->name ?? '',
                'cost' => $data['cost_name'] ?? '',
                'amount' => $data['amount'],

            ];
        });
        $outOfList = CommercialOutOfList::where('request_id', $request->id)->get()->map(function ($commercialDoctor) use ($request) {
            $data = $this->getOutOfListCosts($commercialDoctor, $request);
            return [
                'id' => $commercialDoctor?->id ?? '',
                'doctor' => $commercialDoctor?->name ?? '',
                'speciality' => $commercialDoctor?->speciality?->name ?? '',
                'cost' => $data['cost_name'] ?? '',
                'amount' => $data['amount'],
            ];
        });
        return $this->respond(['doctors' => $commercialDoctors, 'outOfList' => $outOfList]);
    }
    private function getDoctorCosts($commercialDoctor, $request)
    {
        $costType = collect([]);
        $commercialDoctor->doctorCosts()
            ->where('request_id', $commercialDoctor->request_id)->with('costType')->get()->each(function ($doctorCost) use ($costType) {
                $costType = $costType->push($doctorCost->costType);
            });
        $amount = CommercialCostType::where('request_id', $request->id)
            ->whereIntegerInRaw('cost_type_id', $costType->pluck('id'))
            ->sum('amount');
        return array('cost_name' => $costType->pluck('name')->implode(' , '), 'amount' => $amount != 0 ? $amount : '');
    }
    private function getOutOfListCosts($commercialDoctor, $request)
    {
        $costType = collect([]);
        $commercialDoctor->outOfListCosts()
            ->where('request_id', $commercialDoctor->request_id)->with('costType')->get()->each(function ($doctorCost) use ($costType) {
                $costType = $costType->push($doctorCost->costType);
            });
        $amount = CommercialCostType::where('request_id', $request->id)
            ->whereIntegerInRaw('cost_type_id', $costType->pluck('id'))
            ->sum('amount');
        return array('cost_name' => $costType->pluck('name')->implode(' , '), 'amount' => $amount != 0 ? $amount : '');
    }
    public function getCommercialProducts(Request $request)
    {
        $commercial = CommercialRequest::find($request->id);
        $commercialProducts = CommercialProduct::where('request_id', $request->id)->get()->map(function ($commercialProduct) use ($commercial, $request) {
            $commercialUser = $commercial->getCommercialUser;
            $divisionIds = $commercialUser->divisions()?->pluck('line_divisions.id') ?? [];
            $perBrand = Setting::where('key', 'reports_level')->value('value') == 'Brand';
            $id = $perBrand ? $commercialProduct->product->brands?->first()?->id : $commercialProduct->product?->id;
            $name = $perBrand ? $commercialProduct->product->brands?->first()?->name : $commercialProduct->product?->name;
            $budget = Budget::whereIntegerInRaw('div_id', $divisionIds)->where('product_id', $id)->whereYear('from_date', $commercialProduct->created_at)->sum('amount');
            $commercials = $this->commercials($id, $commercialUser);
            $consumed = $commercials->sum('total');
            $count = $commercials->count();
            $no_requests = $count > 0 ? $count : 0;
            return [
                'id' => $id ?? '',
                'product' => $name ?? '',
                'units' => $commercialProduct->units ?? '',
                'ratio' => $commercialProduct->ratio ?? '',
                'amount' => $commercialProduct->amount ?? 0,
                'pharmacies' => CommercialPharmacy::where('request_id', $request->id)
                    ->where('commercial_product_id', $commercialProduct->id)
                    ->with('account')
                    ->get()->pluck('account.name')->implode(' , ') ?? '',
                'budget' => $budget,
                'consumed' => $consumed,
                'remained' => $budget > 0 ? $budget - $consumed : 0,
                'no_of_requests' => $no_requests,
            ];
        });
        return $this->respond($commercialProducts);
    }

    public function commercials($product, $user)
    {
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as id',
            DB::raw('IFNULL(crm_products.id,"") as product_id'),
            DB::raw('IFNULL(crm_commercial_products.ratio,"") as ratio'),
            DB::raw('IFNULL(crm_paid_requests.amount,"") as paid'),
            DB::raw('IFNULL(crm_commercial_requests.amount,"") as amount'),
            DB::raw('COUNT(crm_commercial_requests.id) as no_of_requests'),
        )
            ->selectRaw('crm_paid_requests.amount * crm_commercial_products.ratio / 100  as total')
            ->leftJoin('commercial_products', 'commercial_requests.id', 'commercial_products.request_id')
            ->leftJoin('products', 'commercial_products.product_id', 'products.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', CommercialRequest::class);
                }
            )
            ->leftJoin(
                'paid_requests',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'paid_requests.paidable_id');
                    $join->where('paid_requests.paidable_type', CommercialRequest::class);
                }
            )
            ->orderBy('id', 'DESC')
            ->whereNull('commercial_requests.deleted_at')
            ->where('commercial_requests.user_id', $user->id)
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            // ->where('users.id', $user->id)
            ->whereYear('commercial_requests.created_at', Carbon::now()->format('Y'))
            ->where('commercial_products.product_id', $product)
            ->groupBy(
                "id",
                "products.id",
                "commercial_products.ratio",
                "paid_requests.amount",
                "plan_visit_details.approval"
            )->get();
        return $commercials;
    }

    public function store(CommercialRequestRequest $request)
    {
        // throw new CrmException($request->all());
        $autoApproved = CommercialSetting::where('key', 'auto_approved')->value('value') == 'Yes';
        // throw new CrmException($autoApproved);
        DB::transaction(function () use ($request, $autoApproved) {
            /**@var User $auth */
            $auth = Auth::user();
            $line = $auth->lines()->first();
            $hasApprovable = 1;
            if ($autoApproved) {
                $hasApprovable = $auth->division($line)?->divisionType?->planable?->where('line_id', $line->id)
                    ->first()?->approvables()?->wherePivot('request_type', CommercialRequest::class)->count() > 0;
                if ($auth->hasPosition()) {
                    $hasApprovable = $auth->position()->planable
                        ->first()->approvables()->wherePivot('request_type', CommercialRequest::class)->count() > 0;
                }
            }
            $totalAmount = 0.0;
            $commercial_request = CommercialRequest::create([
                'user_id' => $auth->id,
                'request_type_id' => $request->request_type,
                'description' => $request->description,
                'archived' => 0,
                // 'll' => $request->gpsLocation['lat'],
                // 'lg' => $request->gpsLocation['lng'],
                'from_date' => new Carbon($request->from_date),
                'to_date' => new Carbon($request->to_date),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            foreach ($request->lines as $line) {
                CommercialLine::create([
                    'request_id' => $commercial_request->id,
                    'line_id' => $line,
                ]);
            }
            foreach ($request->divisions as $division) {
                CommercialDivision::create([
                    'request_id' => $commercial_request->id,
                    'div_id' => $division
                ]);
            }
            foreach ($request->types as $type) {
                if (CostType::find($type['id'])->cost->name == 'per request') {
                    $totalAmount += $type['amount'];
                }
                CommercialCostType::create([
                    'request_id' => $commercial_request->id,
                    'cost_type_id' => $type['id'],
                    'amount' => $type['amount'],
                ]);
            }
            foreach ($request->products as $product) {
                $commercial_product = CommercialProduct::create([
                    'request_id' => $commercial_request->id,
                    'product_id' => $product['id'],
                    'ratio' => $product['ratio'],
                    'units' => $product['units'] ?? 0,
                ]);
                foreach ($product['pharmacies'] as $pharmacy) {
                    CommercialPharmacy::create([
                        'commercial_product_id' => $commercial_product->id,
                        'request_id' => $commercial_request->id,
                        'product_id' => $product['id'],
                        'pharmacy_id' => $pharmacy,
                    ]);
                }
            }
            foreach ($request->users as $user => $type) {
                CommercialUser::create([
                    'request_id' => $commercial_request->id,
                    'user_id' => $user,
                ]);
                foreach ($type as $value) {
                    $userCost = CommercialCostType::where('request_id', $commercial_request->id)
                        ->where('cost_type_id', $value)->value('amount');
                    $totalAmount += $userCost;
                    CommercialUserCostType::create([
                        'request_id' => $commercial_request->id,
                        'user_id' => $user,
                        'cost_type_id' => $value
                    ]);
                }
            }
            foreach ($request->doctors as $doctor => $objects) {
                CommercialDoctor::create([
                    'request_id' => $commercial_request->id,
                    'account_id' => NewAccountDoctor::where('doctor_id', $doctor)
                        ->where('from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->where('new_account_doctors.to_date', '>=', (string)Carbon::now())
                            ->orWhere('new_account_doctors.to_date', null))->first()?->account_id,
                    'doctor_id' => $doctor,
                ]);
                if (!empty($objects['types'])) {
                    foreach ($objects['types'] as $value) {
                        $doctorCost = CommercialCostType::where('request_id', $commercial_request->id)
                            ->where('cost_type_id', $value)->value('amount');
                        $totalAmount += $doctorCost;
                        CommercialDoctorCostType::create([
                            'request_id' => $commercial_request->id,
                            'doctor_id' => $doctor,
                            'cost_type_id' => $value
                        ]);
                    }
                }
            };
            if (!empty($request->details)) {
                foreach ($request->details as $detail) {
                    CommercialAgenda::create([
                        'request_id' => $commercial_request->id,
                        'type_id' => $detail['type'],
                        'speaker_name' => $detail['speaker'],
                        'date' => $detail['date'],
                        'from_time' => $detail['from'],
                        'to_time' => $detail['to'],
                        'topic' => $detail['topic'],
                    ]);
                }
            }
            if (!empty($request->doctorInfo)) {
                foreach ($request->doctorInfo as $object) {
                    Doctor::find($object['id'])->update([
                        'mobile' => $object['mobile'] ?? null,
                    ]);
                    Account::find($object['account_id'])->update([
                        'address' => $object['account']['address'] ?? null,
                        'mobile' => $object['mobile'] ?? null,
                    ]);
                }
            }
            if (!empty($request->outOfLists)) {
                foreach ($request->outOfLists as $outOfList) {
                    $record = CommercialOutOfList::create([
                        'request_id' => $commercial_request->id,
                        'name' => $outOfList['name'],
                        'speciality_id' => $outOfList['speciality_id'],
                    ]);
                    foreach ($outOfList['cost_type_id'] as $costType) {
                        CommercialOutOfListCostType::create([
                            'request_id' => $commercial_request->id,
                            'out_of_list_id' => $record->id,
                            'cost_type_id' => $costType,
                        ]);
                    }
                }
            }
            if (!empty($request->categoriesCosts)) {
                foreach ($request->categoriesCosts as $categoriesCost) {
                    CommercialCategoriesCost::create([
                        'request_id' => $commercial_request->id,
                        'payment_method_id' => $categoriesCost['payment_id'],
                        'user_id' => $categoriesCost['user_id'],
                        'cat_id' => $categoriesCost['cat_id'],
                        'type_id' => $categoriesCost['type_id'],
                        'sub_type_id' => $categoriesCost['sub_type_id'],
                        'notes' => $categoriesCost['notes'],
                        'time' => $categoriesCost['time'],
                        'quantity' => $categoriesCost['quantity'],
                        'amount' => $categoriesCost['total'],
                    ]);
                    $totalAmount += $categoriesCost['total'];
                }
            }
            if (!empty($request->payments)) {
                foreach ($request->payments as $payment) {
                    PaidRequest::create([
                        'paidable_id' => $commercial_request->id,
                        'paidable_type' => CommercialRequest::class,
                        'description' => $payment['description'],
                        'payment_method_id' => $payment['payment_method_id'],
                        'user_id' => $payment['user_id'],
                        'type' => $payment['type'],
                        'ref_no' => $payment['ref_no'],
                        'date' => $payment['date'],
                        'amount' => $payment['amount'],
                    ]);
                }
            }

            foreach ($request->attachments as $attachment) {
                Attachment::create([
                    'attachable_id' => $commercial_request->id,
                    'attachable_type' => CommercialRequest::class,
                    'path' => $attachment,
                ]);
            }
            if ($totalAmount == 0.0)
                throw new Exception('Commercial Amount cannot be zero');
            $commercial_request->update(['amount' => $totalAmount]);

            $commercial_request->products
                ->each(fn($product) => $product->update(['amount' => $totalAmount * $product->ratio / 100]));

            $this->validateBudget($request->budgets, $request->products, $totalAmount);

            PlanVisitDetails::firstOrCreate([
                'visitable_id' => $commercial_request->id,
                'visitable_type' => CommercialRequest::class,
                'approval' => $hasApprovable ?  null : 1
            ]);
            PartialPayment::create([
                'request_id' => $commercial_request->id,
            ]);
            ServiceComplete::firstOrCreate([
                'request_id' => $commercial_request->id,
            ], [
                'end_service' => 0,
                'invoice_amount' => $totalAmount,
                'date' => now()
            ]);
            EndCommercialPayment::firstOrCreate([
                'request_id' => $commercial_request->id,
            ], [
                'paid' => 0,
                'invoice_amount' => $totalAmount,
                'date' => now()
            ]);

            if (empty($request->payments)) {
                PaidRequest::firstOrCreate([
                    'paidable_id' => $commercial_request->id,
                    'paidable_type' => CommercialRequest::class,
                ], [
                    'description' => $request->description,
                    'amount' => $totalAmount,
                ]);
            }
            if (!empty($request->selectedDoctors)) {
                foreach ($request->selectedDoctors as $doctor) {
                    Doctor::find($doctor['id'])->update([
                        'national_id' => $doctor['national_id'],
                        'bank_account' => $doctor['bank_account_number'],
                        'bank' => $doctor['bank_name'],
                    ]);
                }
            }
            if (!empty($request->doctorPharmacies)) {
                foreach ($request->doctorPharmacies as $doctor) {
                    foreach ($doctor['pharmacies'] as $pharmacy) {
                        AccountDoctorPharmacy::create([
                            'account_id' => $doctor['account_id'],
                            'doctor_id' => $doctor['id'],
                            'distributor_id' => $pharmacy['distributor_id'],
                            'pharmacy_name' => $pharmacy['name'],
                            'pharmacy_code' => $pharmacy['code'],
                        ]);
                    }
                }
            }

            if (!$auth->hasRole('admin')) {
                NotificationHelper::send(
                    collect($auth->approvableUsers($auth->lines->first()?->id)),
                    new CommercialCreatedNotification('Commercial Created', auth()->user())
                );
            }
        });
        LogActivity::addLog();
        return $this->respondCreated();
    }

    public function validateBudget($budgets, $products, $totalAmount)
    {
        $setting = BudgetSetting::where('key', 'accept_requests_if_budget_is_sufficient')->value('value');
        if ($setting == 'yes') {
            foreach ($budgets as $budget) {
                foreach ($products as $product) {
                    if ($product['id'] == $budget['id'] && $budget['remained'] < round($totalAmount * $product['ratio'] / 100)) {
                        throw new Exception('The Budget Of Product ' . $budget['product'] . ' is exceeded its limit');
                        // throw new CrmException([$budgets, $products, $totalAmount, $setting]);
                    }
                }
            }
        }
    }

    public function doctorTypes(Request $request)
    {
        $doctors = [];

        foreach ($request->doctorCosts as $doctor => $type) {
            foreach ($request->doctors as $doctorId) {
                if ($doctor == $doctorId) {
                    $doctors[] = Doctor::select(
                        'doctors.id',
                        'accounts.name as account',
                        'accounts.id as account_id',
                        'doctors.name',
                        'specialities.name as speciality',
                        'doctors.mobile as mobile',
                        'accounts.address as address',
                    )
                        ->leftJoin('new_account_doctors', 'doctors.id', 'new_account_doctors.doctor_id')
                        ->leftJoin('account_lines', 'new_account_doctors.account_id', 'account_lines.account_id')
                        ->leftJoin('accounts', 'account_lines.account_id', 'accounts.id')
                        ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
                        ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
                        ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
                        ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
                        ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
                        ->where('accounts.active_date', '<=', Carbon::now())
                        ->where('doctors.active_date', '<=', Carbon::now())
                        ->where('new_account_doctors.from_date', '<=', Carbon::now())
                        ->where('doctors.id', $doctor)
                        ->where('accounts.id', $type['account_id'])
                        ->first();
                    break;
                }
            }
        }
        // throw new CrmExcepti?on($request->all());
        return response()->json(['doctors' => $doctors]);
    }

    public function getDoctors(Request $request)
    {
        $doctors = [];
        foreach ($request->doctors as $doctor => $type) {
            $doctors[] = Account::select(
                'doctors.id',
                'accounts.name as account',
                'doctors.name as doctor',
                'doctors.national_id as national_id',
                'doctors.bank_account as bank_account_number',
                'doctors.bank as bank_name',
            )
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
                ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
                ->where('doctors.id', $doctor)
                ->where('accounts.id', $type['account_id'])
                ->first();
        }
        return response()->json(['doctors' => $doctors]);
    }
    public function getDoctorPharmacies(Request $request)
    {

        $doctors = [];
        foreach ($request->doctors as $doctor => $type) {
            $doctors[] = Account::select(
                'doctors.id',
                'accounts.id as account_id',
                'accounts.name as account',
                'doctors.name as doctor',
                // 'account_doctor_pharmacies.distributor_id as distributor_id',
                // 'distributors.id as distributor_id',
                // 'distributors.name as distributor',
                // 'account_doctor_pharmacies.pharmacy_name as pharmacy',
                // 'account_doctor_pharmacies.pharmacy_code as code',
            )
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
                ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
                // ->leftJoin('account_doctor_pharmacies', function ($join) {
                //     $join->on('doctors.id', 'account_doctor_pharmacies.doctor_id')
                //         ->on('accounts.id', 'account_doctor_pharmacies.account_id');
                // })
                // ->leftJoin('distributors', 'account_doctor_pharmacies.distributor_id', 'distributors.id')
                ->where('doctors.id', $doctor)
                ->where('accounts.id', $type['account_id'])
                ->first();
        }
        $doctors = collect($doctors)->map(function ($doctor) {
            return [
                'id' => $doctor->id,
                'doctor' => $doctor->doctor,
                'account' => $doctor->account,
                'account_id' => $doctor->account_id,
                'pharmacies' => AccountDoctorPharmacy::where('account_id', $doctor->account_id)->where('doctor_id', $doctor->id)->get()
            ];
        });
        return response()->json(['total' => $doctors]);
    }
    public function policies()
    {
        $policies = Policy::where('policiable_type', CommercialRequest::class)->get();
        return $this->respond([
            'policies' => $policies,
        ]);
    }

    private function getDates(CommercialRequest $commercial)
    {

        $this->now = Carbon::parse($commercial->created_at);
        // TODO: Need to be cached
        $number_of_month_befor = LinkedParmaciesSetting::where('key', 'number_of_month_befor')->first()?->value;
        $number_of_month_after = LinkedParmaciesSetting::where('key', 'number_of_month_after')->first()?->value;
        for ($i = $number_of_month_befor; $i >= 1; $i--) {
            $this->before[] = (clone $this->now)->startOfMonth()->subMonths($i)->endOfMonth()->format('M Y');
            $this->month_all[] = (clone $this->now)->subMonths($i)->format('M Y');
        }
        $this->month_all[] = (clone $this->now)->format('M Y');
        for ($i = 1; $i <= $number_of_month_after; $i++) {
            $this->after[] = (clone $this->now)->addMonths($i)->format('M Y');
            $this->month_all[] = (clone $this->now)->addMonths($i)->format('M Y');
        }
    }
    public function show($id)
    {
        $commercialRequest = CommercialRequest::find($id);
        $requestDoctorsNo = $commercialRequest->doctors()->count();
        $this->getDates($commercialRequest);
        /**@var User */
        $authUser = Auth::user();
        $costTypes = costType::get()->map(function ($cost) use ($id) {
            return [
                'id' => $cost->id,
                'name' => $cost->name,
                'cost_id' => $cost->cost_id,
                'amount' => CommercialCostType::where('cost_type_id', $cost->id)->where('request_id', $id)->first()->amount ?? '',
            ];
        });
        $commercial = CommercialRequest::where('id', $id)->get()->map(function ($commercial) {
            return [
                'id' => $commercial->id,
                'type' => $commercial->requestType->name,
                'from' => Carbon::parse($commercial->from_date)->toDateString(),
                'to' => Carbon::parse($commercial->to_date)->toDateString(),
                'insertion' => Carbon::parse($commercial->created_at)->toDateString(),
                'line' => $commercial->getCommercialLines ? $commercial->getCommercialLines->pluck('name')->implode(',') : '',
                'user' => $commercial->user->fullname,
                'divisions' => $commercial->getCommercialDivisions ? $commercial->getCommercialDivisions->pluck('name')->implode(',') : '',
                'description' => $commercial->description ?? '',
                'amount' => $commercial->amount ?? '',
                'lat' => (float)$commercial->ll ?? '',
                'lng' => (float)$commercial->lg ?? '',
                'policies' => count($commercial->policies) > 0 ? $commercial->policies : ''
            ];
        });


        $approvalFlow = $commercialRequest->details?->approvalFlows()->count();
        $approvals = collect([]);
        if ($approvalFlow > 0) {
            $approvals = $approvals->merge($commercialRequest->details?->approvalFlows()->get());
        } else {
            $approvals = $approvals->push($commercialRequest->details);
        }
        $approvals = $approvals->map(function ($detail) use ($commercialRequest) {
            return [
                'approved_by' => $detail?->user?->fullname ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'status' => $detail?->approval ?? null,
                'comment' => $detail?->description ?? '',
                'reason' => $detail?->approval == 0 ? $commercialRequest->reasons->first()?->reason : "",
                'date' => is_null($detail?->approval) ? "" : Carbon::parse($detail?->updated_at)->toDateString(),
            ];
        });


        $approvalSetting = ApprovalSetting::where('key', 'commercial_approval_center_flow')->value('value');
        $from = Carbon::parse($commercialRequest->created_at)->firstOfMonth();
        $to = Carbon::parse($commercialRequest->created_at)->endOfMonth();
        $authUserLines = $authUser->lines()->pluck('lines.id')->toArray();
        $approvalData = $authUser->userApprovals($from, $to);
        $linesAapprovables = $approvalData['linesAapprovables'];
        $lines = $commercialRequest->getCommercialLines;
        $data = $authUser->approvalWidget(
            $commercialRequest,
            $authUser,
            CommercialRequest::class,
            $from->toDateString(),
            $to->toDateString(),
            $lines,
            $linesAapprovables,
            $authUserLines
        );
        $dataFlow = $data['linesDataFlow'];
        $currentFlow = $dataFlow?->flow;
        $required = $dataFlow?->required;
        $vacantCount = $data['vacantCount'];
        $haveToApprove = $approvalFlow === $currentFlow - ($vacantCount + 1);



        $perBrand = Setting::where('key', 'reports_level')->value('value') == 'Brand';
        $commercialProducts = collect([]);
        if ($perBrand) {
            $commercialProducts = CommercialProduct::select(
                'commercial_products.id as commercial_product_id',
                'commercial_products.units as units',
                'commercial_products.ratio as ratio',
                'brands.id as id',
                'brands.name as name',
                'products.short_name as brief',
                'commercial_products.id as commercial_product_id',
            )
                ->leftJoin('products', 'commercial_products.product_id', 'products.id')
                ->leftJoin('product_brands', function ($join) {
                    $join->on('products.id', 'product_brands.product_id')
                        ->where('product_brands.from_date', '<=', now())
                        ->whereNull('product_brands.deleted_at')
                        ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', now()));
                })
                ->leftJoin('brands', 'product_brands.brand_id', 'brands.id')
                ->where('request_id', $id)
                ->get()->filter(fn($product) => $product->id != null)
                ->unique('id')
                ->values()
                // throw new CrmException($commercialProducts);
                ->map(function ($commercialProduct) {
                    return [
                        'commercial_product_id' => $commercialProduct->commercial_product_id,
                        'id' => $commercialProduct->id,
                        'product' => $commercialProduct->name ?? '',
                        'brief' => $commercialProduct->brief ?? '',
                        'units' => $commercialProduct->units ?? 0,
                        'ratio' => $commercialProduct->ratio ?? '',
                        'pharmacies' => $this->getPharmacies($commercialProduct),

                    ];
                });
        } else {
            $commercialProducts = CommercialProduct::where('request_id', $id)->get()->map(function ($commercialProduct) use ($id, $commercialRequest) {
                $commercialUser = $commercialRequest->getCommercialUser;
                $divisionIds = $commercialUser->divisions()?->pluck('line_divisions.id') ?? [];
                $budget = Budget::select('amount')->whereIntegerInRaw('div_id', $divisionIds)->where('product_id', $commercialProduct->product_id)->whereYear('from_date', $commercialProduct->created_at)->sum('amount');
                $commercials = $this->commercials($commercialProduct->product_id, $commercialUser);
                $consumed = $commercials->sum('total');
                $count = $commercials->count();
                $no_requests = $count > 0 ? $count : 0;
                return [
                    // 'commercial_product_id' => $commercialProduct->id,
                    'id' => $commercialProduct->product?->id ?? '',
                    'product' => $commercialProduct->product?->name ?? '',
                    'budget' => $budget ?? 0,
                    'consumed' => $consumed,
                    'remained' => $budget > 0 ? $budget - $consumed : 0,
                    'no_requests' => $no_requests,
                    'brief' => $commercialProduct->product?->short_name ?? '',
                    'units' => $commercialProduct->units ?? 0,
                    'ratio' => $commercialProduct->ratio ?? '',
                    'pharmacies' => $this->getPharmacies($commercialProduct),

                ];
            });
        }
        $commercialProducts = $commercialProducts->unique('id')->values();
        $commercialUsers = CommercialUserCostType::where('request_id', $id)->get()->map(function ($commercialUser) use ($id) {
            return [
                'id' => $commercialUser->user?->id ?? '',
                'user' => $commercialUser->user?->fullname ?? '',
                'cost' => $commercialUser->costType?->name ?? '',
                'amount' => CommercialCostType::where('request_id', $id)
                    ->where('cost_type_id', $commercialUser->costType?->id)
                    ->value('amount') ?? '',

            ];
        });


        $settingLinked = LinkedParmaciesSetting::where('key', 'type')->value('linked_from');
        $count = 1;
        $commercialDoctors = CommercialDoctor::where('request_id', $id)->get()
            ->map(function ($commercialDoctor) use (
                $requestDoctorsNo,
                $perBrand,
                &$count,
                $commercialRequest,
                $settingLinked,
                $commercialProducts
            ) {
                $doctorCostPerRequest = $commercialRequest->amount / $requestDoctorsNo;
                $pharmacies = $this->getLinkedPharmacies($commercialRequest, $commercialDoctor->account?->id, $settingLinked, $commercialProducts, $perBrand) ?? [];
                $roi = ($doctorCostPerRequest) > 0 && !empty($pharmacies)
                    ? $pharmacies->sum('all_values') / ($doctorCostPerRequest) : 0;
                $data = $this->getDoctorCosts($commercialDoctor, $commercialRequest);
                return [
                    's' => $count++ ?? '',
                    'id' => $commercialDoctor->doctor?->id ?? '',
                    'doctor_id' => $commercialDoctor->doctor?->id ?? '',
                    'account_id' => $commercialDoctor->account?->id ?? '',
                    'account' => $commercialDoctor->account?->name ?? '',
                    'pharmacies' => $pharmacies,
                    'doctor' => $commercialDoctor->doctor?->name ?? '',
                    'roi' => round($roi, 2) ?? 0,
                    'cost' => $data['cost_name'] ?? '',
                    'amount' => $data['amount'],

                ];
            });
        $countOutOfList = 1;
        $outOfList = CommercialOutOfList::where('request_id', $id)->get()->map(function ($commercialDoctor) use (&$countOutOfList, $commercialRequest, $settingLinked, $commercialProducts) {
            $data = $this->getOutOfListCosts($commercialDoctor, $commercialRequest);
            return [
                'id' => $countOutOfList++ ?? '',
                'id' => $commercialDoctor?->id ?? '',
                'doctor' => $commercialDoctor?->name ?? '',
                'speciality' => $commercialDoctor?->speciality?->name ?? '',
                'cost' => $data['cost_name'] ?? '',
                'amount' => $data['amount'],

            ];
        });

        $agendas = CommercialAgenda::where('request_id', $id)->get()->map(function ($agenda) {
            return [
                'speaker_type' => $agenda->type->name,
                'speaker_name' => $agenda->speaker_name,
                'date' => $agenda->date,
                'from' => $agenda->from_time,
                'to' => $agenda->to_time,
                'topic' => $agenda->topic ?? '',
            ];
        });
        $types = CommercialCostType::where('request_id', $id)->get();
        $attachments = Attachment::where('attachable_id', $id)->where('attachable_type', CommercialRequest::class)->get()
            ->map(function ($commercial) {
                return [
                    'id' => $commercial->attachable_id,
                    'file' => $commercial->path,
                ];
            });
        $feedbacks = RequestFeedback::where('requestable_id', $id)->where('requestable_type', CommercialRequest::class)->get()->map(function ($feedback) {
            return [
                'id' => $feedback->id,
                'request_id' => $feedback->requestable_id,
                'request_type' => $feedback->requestable_type,
                'user' => $feedback->user->fullname,
                'feedback' => $feedback->feedback,
                'auth' => $feedback->user->id == Auth::id() ? 1 : 0,
            ];
        });
        $paids = PaidRequest::where('paidable_id', $id)->where('paidable_type', CommercialRequest::class)->get()->map(function ($paid) {
            return [
                'id' => $paid->id,
                'paid_id' => $paid->paidable_id,
                'paid_type' => $paid->paidable_type,
                'user' => $paid->user?->fullname ?? '',
                'paid' => $paid->type,
                'amount' => $paid->amount ?? '',
                'attach' => Attachment::where('attachable_type', PaidRequest::class)
                    ->where('attachable_id', $paid->id)->first()?->path ?? '',
                'description' => $paid->description ?? '',
                'ref_no' => $paid->ref_no ?? '',
                'date' => $paid->date ?? '',
                'finance_view' => $paid->finance_view ? 'Yes' : 'No',
                'finance_view_date' => $paid->finance_view_date ?? '',
                'finance_comment' => $paid->finance_desc ?? '',
            ];
        });
        $costElements = CommercialCategoriesCost::where('request_id', $id)->get()->map(function ($costElement) {
            return [
                'id' => $costElement->id,
                'category' => $costElement->category?->name ?? '',
                'type' => $costElement->categoryTypes->name ?? '',
                'sub_type' => $costElement->categorySubTypes->name ?? '',
                'cost' => $costElement->amount ?? '',
                'notes' => $costElement->notes ?? '',
                'time' => $costElement->time ?? '',
                'audience' => $costElement->quantity ?? '',
            ];
        });
        $pharmacies = collect([]);
        $model_id = $id;
        $model_type = CommercialRequest::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respond(compact('required', 'haveToApprove', 'approvals', 'outOfList', 'costElements', 'types', 'paids', 'feedbacks', 'commercial', 'commercialProducts', 'agendas', 'attachments', 'commercialUsers', 'commercialDoctors', 'costTypes'));
    }


    public function editCostElements(CommercialRequest $commercial)
    {
        // throw new CrmException($commercial);
        $categoriesCosts = CommercialCategoriesCost::where('request_id', $commercial->id)->get()->map(function ($category) {
            return [
                'payment_id' => $category->payment_method_id,
                'user_id' => $category->user_id,
                'cat_id' => $category->cat_id,
                'type_id' => $category->type_id,
                'sub_type_id' => $category->sub_type_id,
                'notes' => $category->notes,
                'time' => $category->time,
                'quantity' => $category->quantity,
                'total' => $category->amount,
            ];
        });
        return $this->respond($categoriesCosts);
    }

    public function editOutOfList(CommercialRequest $commercial)
    {
        $outOfList = CommercialOutOfList::where('request_id', $commercial->id)->get();
        return $this->respond($outOfList);
    }

    // Update Commercial Request

    public function update(CommercialRequestRequest $request)
    {
        // throw new CrmException($request->all());
        $commercialRequest = CommercialRequest::find($request->id);
        $totalAmount = 0;
        DB::transaction(function () use ($request, $commercialRequest, &$totalAmount) {
            if (!empty($request->lines)) {
                $commercialRequest->lines()->forceDelete();
                foreach ($request->lines as $line) {
                    CommercialLine::create([
                        'request_id' => $commercialRequest->id,
                        'line_id' => $line,
                    ]);
                }
            }
            if (!empty($request->divisions)) {
                $commercialRequest->divisions()->forceDelete();
                foreach ($request->divisions as $division) {
                    CommercialDivision::create([
                        'request_id' => $commercialRequest->id,
                        'div_id' => $division,
                    ]);
                }
            }

            if (!empty($request->allcostTypes)) {
                // Log::info('welcome');
                CommercialCostType::where('request_id', $request->id)->forceDelete();
                for ($i = 0; $i < count($request->allcostTypes); $i++) {
                    if (isset($request->allcostTypes[$i]['amount'])) {
                        CommercialCostType::create(
                            [
                                'cost_type_id' => $request->allcostTypes[$i]['id'],
                                'request_id' => $request->id,
                                'amount' => $request->allcostTypes[$i]['amount'],
                            ]
                        );
                    }
                }
            }


            if (!empty($request->products)) {
                $commercialPharmacies = $commercialRequest->pharmacies;
                $commercialRequest->pharmacies()->forceDelete();
                $commercialRequest->products()->forceDelete();
                $_commercialPharmacies = [];
                foreach ($request->products as $product) {
                    $commercialProduct = CommercialProduct::create([
                        'request_id' => $commercialRequest->id,
                        'product_id' => $product['id'],
                        'units' => $product['units'],
                        'ratio' => $product['ratio'],
                    ]);
                    foreach ($commercialPharmacies as $pharmacy) {
                        if ($commercialProduct->product_id == $pharmacy->product_id) {

                            $_commercialPharmacies[] = [
                                'request_id' => $commercialRequest->id,
                                'commercial_product_id' => $commercialProduct->id,
                                'product_id' => $commercialProduct->product_id,
                                'pharmacy_id' => $pharmacy->id,
                            ];
                        }
                    }
                }
                if (!empty($_commercialPharmacies))
                    CommercialPharmacy::insert($_commercialPharmacies);
            }
            if (!empty($request->doctors)) {
                // Log::info('thanks');

                $commercialRequest->doctorsCost()->forceDelete();
                $commercialRequest->doctors()->forceDelete();
                foreach ($request->doctors as $doctor) {
                    CommercialDoctor::create([
                        'request_id' => $commercialRequest->id,
                        'doctor_id' => $doctor['doctor_id'],
                        'account_id' => NewAccountDoctor::where('doctor_id', $doctor['doctor_id'])
                            ->where('from_date', '<=', Carbon::now())
                            ->where(fn($q) => $q->where('new_account_doctors.to_date', '>=', (string)Carbon::now())
                                ->orWhere('new_account_doctors.to_date', null))->first()?->account_id,
                    ]);
                    if (!empty($doctor['cost'])) {
                        foreach ($doctor['cost'] as $cost) {
                            CommercialDoctorCostType::firstOrCreate([
                                'request_id' => $commercialRequest->id,
                                'doctor_id' => $doctor['doctor_id'],
                                'cost_type_id' => $cost['id']
                            ]);
                        }
                    }
                }
            }
            if (!empty($request->users)) {
                $commercialRequest->usersCost()->forceDelete();
                $commercialRequest->users()->forceDelete();

                foreach ($request->users as $user) {
                    CommercialUser::create([
                        'request_id' => $commercialRequest->id,
                        'user_id' => $user['id'],
                    ]);
                    if (!empty($user['cost'])) {
                        foreach ($user['cost'] as $cost) {
                            CommercialUserCostType::create([
                                'request_id' => $commercialRequest->id,
                                'user_id' => $user['id'],
                                'cost_type_id' => $cost['id']
                            ]);
                        }
                    }
                }
            }
            if (!empty($request->newAttachments)) {
                foreach ($request->newAttachments as $attachment) {
                    Attachment::create([
                        'attachable_id' => $commercialRequest->id,
                        'attachable_type' => CommercialRequest::class,
                        'path' => $attachment,
                    ]);
                }
            }
            if (!empty($request->categoriesCosts)) {
                $commercialRequest->categoriesCosts()->forceDelete();
                foreach ($request->categoriesCosts as $categoriesCost) {
                    CommercialCategoriesCost::create([
                        'request_id' => $commercialRequest->id,
                        'payment_method_id' => $categoriesCost['payment_id'],
                        'user_id' => $categoriesCost['user_id'],
                        'cat_id' => $categoriesCost['cat_id'],
                        'type_id' => $categoriesCost['type_id'],
                        'sub_type_id' => $categoriesCost['sub_type_id'],
                        'notes' => $categoriesCost['notes'],
                        'time' => $categoriesCost['time'],
                        'quantity' => $categoriesCost['quantity'],
                        'amount' => $categoriesCost['total'],
                    ]);
                    $totalAmount += $categoriesCost['total'];
                }
            }
            $commercialRequest->outOfListCostTypes()->forceDelete();
            $commercialRequest->outOfLists()->forceDelete();
            if (!empty($request->outOfLists)) {
                // Log::info('Hello');
                // throw new CrmException('Hello');
                foreach ($request->outOfLists as $outOfList) {
                    $record = CommercialOutOfList::create([
                        'request_id' => $commercialRequest->id,
                        'name' => $outOfList['name'],
                        'speciality_id' => $outOfList['speciality_id'],
                    ]);
                    // if ($outOfList['cost_type_id']) {
                    //     foreach ($outOfList['cost_type_id'] as $costType) {
                    //         CommercialOutOfListCostType::create([
                    //             'request_id' => $commercialRequest->id,
                    //             'out_of_list_id' => $record->id,
                    //             'cost_type_id' => $costType,
                    //         ]);
                    //     }
                    // }
                }
            }
        });
        if ($request->feedback) {
            $feedback = RequestFeedback::where('requestable_id', $request->id)->where('requestable_type', CommercialRequest::class)->first();
            $feedback->feedback = $request->feedback;
            $feedback->save();
        }
        $commercialRequest->from_date = $request->from_date;
        $commercialRequest->request_type_id = $request->type_id;
        $commercialRequest->to_date = $request->to_date;
        $commercialRequest->description = $request->description;
        $commercialRequest->amount = $this->totalAmount($commercialRequest) + $totalAmount;
        $commercialRequest->save();

        $commercialRequest->products
            ->each(fn($product) => $product->update(['amount' => $totalAmount * $product->ratio / 100]));
        return $this->respondSuccess();
    }
    public function totalAmount($commercial)
    {
        $amount = 0.0;
        CommercialCostType::where('request_id', $commercial->id)->get()->each(function ($commercialCostType) use ($commercial, &$amount) {
            $costData = $commercialCostType->costType->cost;
            if ($costData->name == 'per request') $amount += $commercialCostType->amount;
            if ($costData->name == 'per employee') {
                $usersCount = $commercial->usersCost()->where('cost_type_id', $commercialCostType->cost_type_id)->count();
                $amount += ($usersCount * $commercialCostType->amount);
            }
            if ($costData->name == 'per doctor') {
                $doctorsCount = $commercial->doctorsCost()->where('cost_type_id', $commercialCostType->cost_type_id)->count();
                // throw new CrmException($commercial->doctorsCost()->get());
                $amount += ($doctorsCount * $commercialCostType->amount);
                // throw new CrmException($commercial->doctorsCost());
            }
        });
        return $amount;
    }

    public function getLinkedPharmacies($commercial, $account_id, $setting, $userProducts, $perBrand)
    {
        $pharmacies = [];
        if ($account_id) {
            if ($setting == 'mapping') {
                $pharmacies =  Mapping::select('id', 'code', 'name', 'distributor_id')
                    ->forAccount($account_id)
                    ->get();

                $pharmacyIds = $pharmacies->pluck("id")->toArray();

                // $months = collect();
                $salesByPharmacy = collect();

                foreach ($userProducts as $product) {
                    $sales = $this->months($this->month_all, $product, $pharmacyIds, $perBrand)
                        ->transform(function ($sale) {
                            $sale->month = Carbon::parse($sale->date_key)->format('M Y');
                            $sale->units = (int) $sale->units;
                            $sale->value = (float) round($sale->value,2);

                            unset($sale->year);

                            return $sale;
                        });

                    $salesByPharmacy = $salesByPharmacy->merge(
                        [
                            [
                                "product_id" => $product['id'],
                                'sales' => $sales->groupBy(['pharmacy_id', 'date_key'])
                            ]
                        ]
                    );
                }
                $pharmacies->transform(function ($pharmacy) use ($userProducts, $salesByPharmacy) {
                    $beforeValue = 0;
                    $afterValue = 0;
                    $allValue = 0;

                    $pharmacy->products = collect();

                    foreach ($userProducts as $product) {
                        $perProductPharmacySales = $salesByPharmacy->where('product_id', $product['id'])?->first()['sales'];
                        if (!$perProductPharmacySales) continue;
                        $sales = $perProductPharmacySales[$pharmacy->id]->values()->collapse();
                        $retriveSalesAroundDate = $this->retriveSalesAroundDate($this->now, $sales);

                        $pharmacy->products = $pharmacy->products->merge([
                            [
                                'product_id' => $product['id'],
                                'product' => $product['product'],
                                'ratio' => $product['ratio'],
                                'units' => $product['units'],
                                'months' => $sales,
                                'months_before' => $retriveSalesAroundDate['months_before'],
                                'now' => $retriveSalesAroundDate['now'],
                                'months_after' => $retriveSalesAroundDate['months_after'],
                            ]
                        ]);

                        $beforeValue += collect($retriveSalesAroundDate['months_before'])->sum('value');
                        $afterValue += collect($retriveSalesAroundDate['months_after'])->sum('value');
                        $allValue += $sales->sum('value');
                    }

                    $pharmacy->value_before = round($beforeValue, 2);
                    $pharmacy->value_after = round($afterValue, 2);
                    $pharmacy->all_values = round($allValue, 2);


                    return $pharmacy;
                });

            }
            if ($setting == 'unified') {
                $linkedPharmacies = LinkedPharmacy::where('account_id', $account_id)
                    ->where('pharmable_type', MappingUnifiedCode::class)->get()->pluck('pharmable_id');
                $pharmacies = MappingUnifiedCode::select('id', 'name')->whereIntegerInRaw('id', $linkedPharmacies)->get()
                    ->map(function ($pharmacy) use ($userProducts, $perBrand) {
                        return [
                            'id' => $pharmacy->id,
                            'name' => $pharmacy->name,
                            'products' => $this->getSalesData($userProducts, $pharmacy, $perBrand),

                        ];
                    });
            }
            return $pharmacies;
        }
    }

    private function retriveSalesAroundDate(Carbon $targetDateObject, Collection $sales): array
    {
        $data_before = [];
        $data_after = [];
        $now = [];

        $targetDate = $targetDateObject->clone()->firstOfMonth();

        $sales->each(function ($item) use ($targetDate, &$data_before, &$data_after, &$now) {

            $date = Carbon::parse($item->date_key);

            if ($date->clone()->firstOfMonth()->lt($targetDate)) {
                $data_before[] = $item;
            } else if ($date->clone()->firstOfMonth()->gt($targetDate)) {
                $data_after[] = $item;
            } else {
                $now = $item;
            }
        });

        return [
            "months_before" => $data_before,
            "now" => $now,
            "months_after" => $data_after,
        ];
    }

    private function getPharmacies($commercialProduct)
    {
        $pharmacySetting = CommercialPharmacySetting::first();
        $pharmacies = collect([]);
        if ($pharmacySetting->name == 'List') {
            $commercialProduct->pharmacies->each(function ($pharmacy) use ($pharmacies) {
                $pharmacies = $pharmacies->push($pharmacy?->account?->name);
            });
        }
        return $pharmacies->toArray();
    }




    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $commercial = CommercialRequest::find($id);
        $commercial->lines()->delete();
        $commercial->divisions()->delete();
        $commercial->types()->delete();
        $commercial->products()->delete();
        $commercial->pharmacies()->delete();
        $commercial->usersCost()->delete();
        $commercial->users()->delete();
        $commercial->doctorsCost()->delete();
        $commercial->doctors()->delete();
        $commercial->attachments()->delete();
        $commercial->agendas()->delete();
        $commercial->categoriesCosts()->delete();
        $commercial->delete();
        $model_id = $commercial->id;
        $model_type = CommercialRequest::class;

        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }
    public function doctorProducts(Request $request)
    {
        $request_doctors = $request->all()['doctors'];
        $request_products = collect($request->all()['products'])->pluck('id');
        $products_ids = array_filter($request_products->toArray(), fn($value) => !is_null($value));
        $products = Product::whereIn('id', $products_ids)->get();

        $from = $this->getDocMonth()[0];
        $to = $this->getDocMonth()[count($this->getDocMonth()) - 1];

        $accounts_id = db::table('new_account_doctors')
            ->whereIn('doctor_id', $request_doctors)
            ->pluck('account_id');

        $mappings = Mapping::where('mapping_type_id', 2)->get()->pluck('id');
        $sales = Sale::select(
            'sales.*',
            'linked_pharmacies.*'
        )
            ->leftJoin('linked_pharmacies', 'sales.mapping_id', 'linked_pharmacies.pharmable_id')
            ->whereBetween('sales.date', [$from, $to])
            ->whereIn('sales.product_id', $products_ids)
            // ->whereIn('sales.mapping_id',$mappings)
            // ->whereIn('linked_pharmacies.account_id',$accounts_id)
            ->get();

        return response()->json(['products' => $products]);
    }


    public function feedback($id, Request $request)
    {
        $commercial = CommercialRequest::find($id);
        $user_id = Auth::id();
        $commercial_feedback = new RequestFeedback();
        $commercial_feedback->requestable_type = CommercialRequest::class;
        $commercial_feedback->requestable_id = $commercial->id;
        $commercial_feedback->feedback = $request->feedback;
        $commercial_feedback->user_id = $user_id;
        $commercial_feedback->save();
        return $this->respondSuccess();
    }

    private function getSales($dates, $pharmacyIds, $product, $perBrand)
    {
        // Get products efficiently
        $products = $perBrand
            ? Brand::find($product['id'])?->products()->pluck('products.id')->toArray() ?? []
            : [$product['id']];
        // Create base combinations of all pharmacies with all date periods
        $basePharmaciesQueries = [];
        foreach ($dates as $dateArray) {
            $year = $dateArray[0];
            $month = $dateArray[1];
            $dateKey = $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT);

            $basePharmaciesQueries[] = DB::table('mappings as m')
                ->select([
                    'm.id as pharmacy_id',
                    DB::raw("'{$dateKey}' as date_key"),
                    DB::raw("{$year} as year"),
                    DB::raw("{$month} as month"),
                    DB::raw('0 as units'),
                    DB::raw('0 as value')
                ])
                ->whereIn('m.id', $pharmacyIds);
        }

        // Union all base pharmacy queries
        $basePharmacies = $basePharmaciesQueries[0];
        for ($i = 1; $i < count($basePharmaciesQueries); $i++) {
            $basePharmacies = $basePharmacies->unionAll($basePharmaciesQueries[$i]);
        }

        $product_avg_price = getAvgProductPriceQuery();

        // Main sales query
        $salesQuery = DB::table('sales')
            ->select([
                'mappings.id as pharmacy_id',
                DB::raw("CONCAT(YEAR(crm_sales_details.date), '-', LPAD(MONTH(crm_sales_details.date), 2, '0')) as date_key"),
                DB::raw("YEAR(crm_sales_details.date) as year"),
                DB::raw("MONTH(crm_sales_details.date) as month"),
                DB::raw('COALESCE(SUM(crm_sales_details.quantity), 0) as units'),
                DB::raw("
                CASE 
                    WHEN COALESCE(SUM(crm_sales_details.value), 0) = 0
                    THEN COALESCE(SUM($product_avg_price * crm_sales_details.quantity), 0)
                    ELSE COALESCE(SUM(crm_sales_details.value), 0)
                END
            AS value"),
            ])
            ->leftJoin('sales_details', 'sales.id', '=', 'sales_details.sale_id')
            ->leftJoin('mapping_sale', 'sales.id', '=', 'mapping_sale.sale_id')
            ->leftJoin('mappings', 'mapping_sale.mapping_id', '=', 'mappings.id')
            ->whereIn('sales.product_id', $products)
            ->whereIn('mappings.id', $pharmacyIds)
            ->whereTupleIn(
                [DB::raw("YEAR(crm_sales_details.date)"), DB::raw("MONTH(crm_sales_details.date)")],
                $dates
            )
            ->groupBy([
                'sales_details.date',
                'mappings.id'
            ]);

        // Combine base pharmacies with actual sales data using UNION
        // Then aggregate to get final results per pharmacy
        $results = DB::query()
            ->fromSub($basePharmacies->unionAll($salesQuery), 'combined')
            ->select([
                'pharmacy_id',
                'date_key',
                'year',
                'month',
                DB::raw('ROUND(SUM(units),0) as units'),
                DB::raw('ROUND(SUM(value),2) as value')
            ])
            ->groupBy(['pharmacy_id', 'date_key', 'year', 'month'])
            ->get();

        return $results;
    }


    public function doctorMonth(CommercialRequest $commercial)
    {
        $month_all = [];
        $before = [];
        $after = [];
        $now = Carbon::parse($commercial->created_at)->format('M Y');
        $number_of_month_befor = LinkedParmaciesSetting::where('key', 'number_of_month_befor')->first()?->value;
        $number_of_month_after = LinkedParmaciesSetting::where('key', 'number_of_month_after')->first()?->value;
        for ($i = $number_of_month_befor; $i >= 1; $i--) {
            array_push($before, Carbon::parse($commercial->created_at)->startOfMonth()->subMonths($i)->endOfMonth()->format('M Y'));
            array_push($month_all, Carbon::parse($commercial->created_at)->subMonths($i)->format('M Y'));
        }
        array_push($month_all, Carbon::parse($commercial->created_at)->format('M Y'));
        for ($i = 1; $i <= $number_of_month_after; $i++) {
            array_push($after, Carbon::parse($commercial->created_at)->addMonths($i)->format('M Y'));
            array_push($month_all, Carbon::parse($commercial->created_at)->addMonths($i)->format('M Y'));
        }
        return response()->json([
            'month_all' => $month_all,
            'now' => $now,
            'before' => $before,
            'after' => $after,
        ]);
    }

    private function getSalesData($products, $pharmacy, $perBrand)
    {

        $months = collect([]);
        foreach ($products as $product) {
            $months = $months->push(collect([
                'product_id' => $product['id'],
                'product' => $product['product'],
                'ratio' => $product['ratio'],
                'units' => $product['units'],
                'months' => $this->months($this->month_all, $product, $pharmacy, $perBrand),
                'months_before' => $this->beforeMonths($this->before, $product, $pharmacy, $perBrand),
                'months_after' => $this->afterMonths($this->after, $product, $pharmacy, $perBrand),
                'now' => $this->currentMonth($this->now, $product, $pharmacy, $perBrand)
            ]));
        }
        return $months;
    }

    private function months($dates_all, $product, $pharmacyIds, $perBrand)
    {
        $dates = [];
        foreach ($dates_all as $date) {
            $date =  Carbon::parse($date);
            $dates[] = ["$date->year", "$date->month"];
        }

        return  $this->getSales($dates, $pharmacyIds, $product, $perBrand);
    }

    public function getEditedData($id)
    {
        $commercialRequest = CommercialRequest::find($id);
        $setting = ListType::first()->type === 'Default List';
        $lines = $commercialRequest->lines->pluck('line_id')->toArray();
        $divisions = $commercialRequest->divisions->pluck('div_id')->toArray();
        $costTypes = costType::get()->map(function ($cost) use ($id) {
            return [
                'id' => $cost->id,
                'name' => $cost->name,
                'cost_id' => $cost->cost_id,
                'amount' => CommercialCostType::where('cost_type_id', $cost->id)->where('request_id', $id)->first()->amount ?? '',
            ];
        });
        $commercial = CommercialRequest::where('id', $id)->get()->map(function ($commercial) {
            return [
                'id' => $commercial->id,
                'from_date' => Carbon::parse($commercial->from_date)->toDateString(),
                'to_date' => Carbon::parse($commercial->to_date)->toDateString(),
                'type_id' => $commercial->request_type_id,
                'description' => $commercial->description ?? '',
            ];
        })->collapse();
        // $commercialProducts = collect([]);
        $commercialProducts = LineProduct::whereIn('line_id', $lines)
            ->where("line_products.from_date", "<=", Carbon::now())
            ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                ->orWhere('line_products.to_date', null));

        $product_brand_level = Setting::where('key', 'reports_level')->value('value');
        if ($product_brand_level == 'Brand') {
            $commercialProducts = $commercialProducts
                ->with('product')->get()
                ->pluck('product')
                ->map(function ($product) use ($id) {
                    $commercialData = CommercialProduct::where('request_id', $id)->where('product_id', $product->id)->first();
                    return [
                        'id' => $product->id,
                        'product' => $product?->brands?->first() ? $product?->brands?->first()?->name : $product->name,
                        'units' => $commercialData->units ?? 0,
                        'ratio' => $commercialData->ratio ?? '',
                    ];
                })->unique('product')->filter(fn($product) => $product['product'] != null)->values();
        } else {
            $commercialProducts = $commercialProducts->with('product')->get()
                ->pluck('product')->map(function ($product) use ($id) {
                    $commercialData = CommercialProduct::where('request_id', $id)->where('product_id', $product->id)->first();
                    return [
                        'id' => $product->id,
                        'product' => $product->name,
                        'units' => $commercialData->units ?? 0,
                        'ratio' => $commercialData->ratio ?? '',
                    ];
                });
        }

        $doctors = $this->activeList($lines, $divisions, $setting, $commercialRequest->id);

        $types = CommercialCostType::where('request_id', $id)->get();
        $attachments = Attachment::where('attachable_id', $id)->where('attachable_type', CommercialRequest::class)->get()
            ->map(function ($attach) {
                return [
                    'id' => $attach->attachable_id,
                    'attach_id' => $attach->id,
                    'file' => $attach->path,
                    'edited' => null,
                ];
            });
        $feedbacks = RequestFeedback::where('requestable_id', $id)->where('requestable_type', CommercialRequest::class)->get()->map(function ($feedback) {
            return [
                'id' => $feedback->id,
                'request_id' => $feedback->requestable_id,
                'request_type' => $feedback->requestable_type,
                'user' => $feedback->user->fullname,
                'feedback' => $feedback->feedback,
                'auth' => $feedback->user->id == Auth::id() ? 1 : 0,
            ];
        });


        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        foreach ($lines as $line_id) {
            $line = Line::find($line_id);
            $lineUserPosition = LineUserPosition::where('line_id', $line_id)->get()->pluck('user_position_id');
            $userPosition = UserPosition::whereIn('id', $lineUserPosition)
                ->where('from_date', '<=', now())
                ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>', (string)Carbon::now()))
                ->with('user')->get()->pluck('user');
            if ($user->hasRole('admin')) {
                $users = $users->push($line->users, $userPosition);
            } else {
                $users = $users->push($user->allBelowUsers($line), $user->allAboveUsers($line), $userPosition);
            }
        }
        $users = $users->collapse()->unique('id')->values()->map(function ($CommercialUser) use ($id) {
            return [
                'id' => $CommercialUser->id ?? '',
                'employee' => $CommercialUser->fullname ?? '',
                'requested' => CommercialUser::where('request_id', $id)
                    ->where('user_id', $CommercialUser->id)->exists(),
                'cost' => CommercialUserCostType::where('request_id', $id)
                    ->where('user_id', $CommercialUser->id)->get()->map(function ($item) {
                        return
                            [
                                'id' => $item->costType?->id,
                                'name' => $item->costType?->name,
                            ];
                    }) ?? '',
            ];
        });
        return $this->respond(compact('lines', 'divisions', 'commercialProducts', 'types', 'feedbacks', 'commercial', 'attachments', 'doctors', 'users', 'costTypes'));
    }

    public function getEditedProducts(Request $request, $id)
    {
        $lines = $request->lines;
        $commercialProducts = LineProduct::whereIn('line_id', $lines)
            ->where("line_products.from_date", "<=", Carbon::now())
            ->where(fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                ->orWhere('line_products.to_date', null));

        $product_brand_level = Setting::where('key', 'reports_level')->value('value');
        if ($product_brand_level == 'Brand') {
            $commercialProducts = $commercialProducts
                ->with('product')->get()
                ->pluck('product')
                ->map(function ($product) use ($id) {
                    $commercialData = CommercialProduct::where('request_id', $id)->where('product_id', $product->id)->first();
                    return [
                        'id' => $product->id,
                        'product' => $product?->brands?->first() ? $product?->brands?->first()?->name : $product->name,
                        'units' => $commercialData->units ?? 0,
                        'ratio' => $commercialData->ratio ?? '',
                    ];
                })->unique('product')->filter(fn($product) => $product['product'] != null)->values();
        } else {
            $commercialProducts = $commercialProducts->with('product')->get()
                ->pluck('product')->map(function ($product) use ($id) {
                    $commercialData = CommercialProduct::where('request_id', $id)->where('product_id', $product->id)->first();
                    return [
                        'id' => $product->id,
                        'product' => $product->name,
                        'units' => $commercialData->units ?? '',
                        'ratio' => $commercialData->ratio ?? '',
                    ];
                });
        }
        return $this->respond($commercialProducts);
    }

    public function getEditedDoctors(Request $request)
    {
        $divisions = $request->divisions;
        $setting = ListType::first()->type == 'Default List' ? true : false;
        $lines = LineDivision::whereIntegerInRaw('id', $request->divisions)->pluck('line_id')->toArray();
        $doctors = $this->activeList($lines, $divisions, $setting, $request->id);
        return $this->respond($doctors);
    }

    private function activeList($lines, $divisions, $setting, $commercialId)
    {
        $doctors =  Account::select(
            'doctors.id as id',
            'accounts.id as account_id',
            // 'lines.name as line',
            // 'lines.id as line_id',
            // 'line_divisions.name as division',
            // 'line_divisions.id as div_id',
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
            DB::raw('case when max(crm_commercial_doctors.id) is null then 0 else 1 end as requested'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_accounts.code,"") as code'),
            'account_types.name as account_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            'specialities.name as speciality',
        )
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->whereNull('accounts.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->where(fn($q) => $q->whereNull('accounts.inactive_date')
                ->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->whereNull('doctors.inactive_date')
                ->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->join('account_types', 'accounts.type_id', 'account_types.id')
            ->join('account_lines', function ($join) use ($lines, $divisions) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->whereNull('account_lines.deleted_at')
                    ->where('account_lines.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                        ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
                if (!empty($lines)) {
                    $join->whereIntegerInRaw('account_lines.line_id', $lines);
                }
                if (!empty($divisions)) {
                    $join->whereIntegerInRaw('account_lines.line_division_id', $divisions);
                }
            });
        if (!$setting) {
            $doctors = $doctors
                ->join(
                    'new_account_doctors',
                    function ($join) use ($lines, $divisions) {
                        $join->on('accounts.id', 'new_account_doctors.account_id')
                            ->on('account_lines.id', 'new_account_doctors.account_lines_id')
                            ->whereNull('new_account_doctors.deleted_at')
                            ->where('new_account_doctors.from_date', '<=', Carbon::now())
                            ->where(fn($q) => $q->whereNull('new_account_doctors.to_date')
                                ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));
                        if (!empty($lines)) {
                            $join->whereIntegerInRaw('new_account_doctors.line_id', $lines);
                        }
                    }
                );
        } else {
            $doctors = $doctors
                ->join('new_account_doctors', function ($join) use ($lines, $divisions) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereNull('new_account_doctors.deleted_at')
                        ->where('new_account_doctors.from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->whereNull('new_account_doctors.to_date')
                            ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));
                    if (!empty($lines)) {
                        $join->whereIntegerInRaw('new_account_doctors.line_id', $lines);
                    }
                });
        }

        $doctors->join('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin(
                'commercial_doctors',
                fn($q) => $q->on('commercial_doctors.doctor_id', 'doctors.id')
                    ->where('commercial_doctors.request_id', $commercialId)
            )
            ->leftJoin(
                'commercial_doctor_cost_types',
                fn($q) => $q->on('commercial_doctor_cost_types.doctor_id', 'doctors.id')
                    ->where('commercial_doctor_cost_types.request_id', $commercialId)
            )
            ->orderByRaw('requested DESC, crm_doctors.ucode ASC')
            ->where(
                fn($q) => $q->where('accounts.id', 'Like', '%' . request('query') . '%')
                    ->orWhere('accounts.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('accounts.code', 'Like', '%' . request('query') . '%')
                    ->orWhere('doctors.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('line_divisions.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('doctors.id', 'Like', '%' . request('query') . '%')
                    ->orWhere('specialities.name', 'Like', '%' . request('query') . '%')
            );

        return  $doctors->groupBy(
            "accounts.id",
            "doctors.id",
            // "lines.id",
            // "line_divisions.id",
        )->paginate(300)
            ->through(function ($commercialDoctor) use ($commercialId) {
                return [
                    'doctor_id' => $commercialDoctor->doctor_id ?? '',
                    'line' => $commercialDoctor->line ?? '',
                    'division' => $commercialDoctor->division ?? '',
                    'account' => $commercialDoctor->account ?? '',
                    'doctor' => $commercialDoctor->doctor ?? '',
                    'speciality' => $commercialDoctor->speciality ?? '',
                    'requested' => $commercialDoctor->requested,
                    'cost' => CommercialDoctorCostType::where('request_id', $commercialId)
                        ->where('doctor_id', $commercialDoctor->doctor_id)->get()->map(function ($item) {
                            return
                                [
                                    'id' => $item->costType?->id,
                                    'name' => $item->costType?->name,
                                ];
                        }) ?? '',
                ];
            });
    }

    public function getUsers(Request $request, $id)
    {
        $lines = $request->lines;
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        foreach ($lines as $line_id) {
            $line = Line::find($line_id);
            $lineUserPosition = LineUserPosition::where('line_id', $line_id)->get()->pluck('user_position_id');
            $userPosition = UserPosition::whereIn('id', $lineUserPosition)
                ->where('from_date', '<=', now())
                ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>', (string)Carbon::now()))
                ->with('user')->get()->pluck('user');
            if ($user->hasRole('admin')) {
                $users = $users->push($line->users, $userPosition);
            } else {
                $users = $users->push($user->allBelowUsers($line), $user->allAboveUsers($line), $userPosition);
            }
        }
        $users = $users->collapse()->unique('id')->values()->map(function ($CommercialUser) use ($id) {
            return [
                'id' => $CommercialUser->id ?? '',
                'employee' => $CommercialUser->line?->name ?? '',
                'employee' => $CommercialUser->fullname ?? '',
                'requested' => CommercialUser::where('request_id', $id)
                    ->where('user_id', $CommercialUser->id)->exists(),
                'cost' => CommercialUserCostType::where('request_id', $id)
                    ->where('user_id', $CommercialUser->id)->get()->map(function ($item) {
                        return
                            [
                                'id' => $item->costType?->id,
                                'name' => $item->costType?->name,
                            ];
                    }) ?? '',
            ];
        });
        return $this->respond($users);
    }

    public function removeCommercialAttach($id)
    {
        $attachment = Attachment::find($id);
        // throw new CrmException($attachment);
        if ($attachment) $attachment->forceDelete();
        return $this->respondSuccess();
    }

    public function import(ImportRequest $request)
    {
        CommercialDoctor::import($request);
        return $this->respondSuccess();
    }
    public function getApprovalUser(Request $request)
    {
        $commercial = CommercialRequest::find($request->id);
        $approvalFlow = $commercial->details?->approvalFlows()->count();
        $data = collect([]);
        if ($approvalFlow > 0) {
            $data = $data->merge($commercial->details?->approvalFlows()->get());
        } else {
            $data = $data->push($commercial->details);
        }
        $data = $data->map(function ($detail) use ($commercial) {
            return [
                'id' => $detail?->id ?? null,
                'commercial_id' => $commercial?->id ?? null,
                'status' => $detail?->approval ?? null,
                'reason' => $detail?->approval == 0 ? $commercial->reasons->first()?->reason : "",
                'approved_by' => $detail?->user?->fullname ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'date' => is_null($detail?->approval) ? "" : Carbon::parse($detail?->updated_at)->toDateString(),
                'actions' => ''

            ];
        });
        $mainApproval = $commercial->details?->approval;
        $fields = ['id', 'commercial_id', 'status', 'reason', 'approved_by', 'position', 'date', 'actions', 'delete'];
        return $this->respond(['approvals' => $data, 'fields' => $fields, 'mainApproval' => $mainApproval]);
        // throw new CrmException($request->all());
    }
    public function updateApprovalUser(Request $request)
    {
        $approvals = $request->approvals;
        $commercial = CommercialRequest::find($approvals[0]['commercial_id']);
        $approvalFlows = $commercial->details?->approvalFlows->count();
        if ($approvalFlows > 0) {
            foreach ($approvals as $approval) {
                ApprovalFlowUser::find($approval['id'])->update(['approval' => $approval['status']]);
            }
        }
        if ($request->mainApproval == null) {
            $commercial->details()->update(['approval' => $request->mainApproval, 'user_id' => null]);
        }
        if ($request->mainApproval == null) {
            $commercial->details()->update(['approval' => $request->mainApproval]);
        }
        return $this->respondSuccess();
    }

    public function deleteApprovalUser($id)
    {
        // throw new CrmException(ApprovalFlowUser::find($id));
        $model_id = $id;
        $model_type = ApprovalFlowUser::class;
        LogActivity::addLog($model_id, $model_type);
        ApprovalFlowUser::find($id)->delete();
        return $this->respondSuccess();
    }
}
