<?php

namespace App\Http\Controllers;

use App\Account;
use App\Action;
use App\Classes;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use App\Http\Controllers\Controller;
use App\Http\Requests\AccountDoctorsRequest;
use App\Line;
use App\Models\NewAccountDoctor;
use App\Permission;
use App\Speciality;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class NewAccountDoctorController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $lines = Line::orderBy('sort', 'ASC')->select('lines.id', 'lines.name')->where('deleted_at', null)->get()->filter(function ($line) {
            return $line->to_date == null || $line->to_date >= (string)Carbon::now();
        });
        $doctors = Doctor::select('doctors.id', 'doctors.name')->where('deleted_at', null)->get()->filter(function ($doctor) {
            return $doctor->inactive_date == null || $doctor->inactive_date >= (string)Carbon::now();
        });
        $specialities = Speciality::select('specialities.id', 'specialities.name')->where('deleted_at', null)->get()->filter(function ($speciality) {
            return $speciality->inactive_date == null || $speciality->inactive_date >= (string)Carbon::now();
        });
        $classes = Classes::select('classes.id', 'classes.name')->where('deleted_at', null)->get()->filter(function ($class) {
            return $class->inactive_date == null || $class->inactive_date >= (string)Carbon::now();
        });
        return response()->json([
            'lines' => $lines, 
            'doctors' => $doctors,
            'specialities' => $specialities,
            'classes' => $classes,
        ]);
    }

    public function getAccountDoctors(Account $account)
    {
        $accountDoctors = DB::table('new_account_doctors')
            ->select(
                'new_account_doctors.id',
                'new_account_doctors.account_lines_id',
                'doctors.name as doctor',
                'accounts.name as account',
                'lines.name as line',
                // 'line_divisions.name as division',
                // 'bricks.name as brick',
                'new_account_doctors.from_date',
                'new_account_doctors.to_date',
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                'classes.name as class',
                'classes.id as class_id',
            )
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', '=', 'doctors.id')
            ->leftJoin('accounts', 'new_account_doctors.account_id', '=', 'accounts.id')
            // ->leftJoin('account_lines', 'new_account_doctors.account_lines_id', '=', 'account_lines.id')
            // ->leftJoin('line_divisions', 'account_lines.line_division_id', '=', 'line_divisions.id')
            // ->leftJoin('bricks', 'account_lines.brick_id', '=', 'bricks.id')
            ->leftJoin('lines', 'new_account_doctors.line_id', '=', 'lines.id')
            ->leftJoin('classes', 'new_account_doctors.class_id', '=', 'classes.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->where('new_account_doctors.deleted_at', '=', null)
            ->where('new_account_doctors.account_id', '=', $account->id)
            ->orderBy('new_account_doctors.account_lines_id', 'Asc')
            ->get();
        LogActivity::addLog();

        return response()->json(['accountDoctors' => $accountDoctors]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(AccountDoctorsRequest $request)
    {
        $accountDoctor = NewAccountDoctor::create([
            'account_id' => $request->account_id,
            'doctor_id' => $request->doctor_id,
            'line_id' => $request->line_id,
            'from_date' => $request->from_date,
            'to_date' => $request->to_date,
            'class_id' => $request->class_id,
        ]);
        $model_id = $accountDoctor->id;
        $model_type = NewAccountDoctor::class;
        LogActivity::addLog($model_id, $model_type);


        return response()->json(['status' => 'success', 'accountDoctor' => $accountDoctor]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $account_doctor = DB::table('new_account_doctors')
            ->select(
                'new_account_doctors.id',
                'new_account_doctors.doctor_id',
                'new_account_doctors.account_id',
                'new_account_doctors.line_id',
                'new_account_doctors.class_id',
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                'new_account_doctors.from_date',
                'new_account_doctors.to_date'
            )
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes', 'doctors.class_id', 'classes.id')
            ->where('new_account_doctors.id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = NewAccountDoctor::class;
        LogActivity::addLog($model_id, $model_type);

        return response()->json(['account_doctor' => $account_doctor]);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(AccountDoctorsRequest $request, $id)
    {
        $account_doctor = NewAccountDoctor::find($id);
        $doctor = Doctor::find($account_doctor->doctor_id);
        $account_doctor->doctor_id = $request->input('doctor_id');
        $account_doctor->line_id = $request->input('line_id');
        $account_doctor->from_date = $request->input('from_date');
        $account_doctor->to_date = $request->input('to_date');
        $account_doctor->class_id = $request->input('class_id'); // Store class_id in new_account_doctors
        $account_doctor->save();
        $doctor->speciality_id = $request->input('speciality_id');
        $doctor->save();
        $model_id = $id;
        $model_type = NewAccountDoctor::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $accountDoctor = NewAccountDoctor::find($id);
        if ($accountDoctor) {
            $accountDoctor->delete();
        }
        $model_id = $id;
        $model_type = NewAccountDoctor::class;

        LogActivity::addLog($model_id, $model_type);

        return response()->json(['status' => 'success']);
    }
}
