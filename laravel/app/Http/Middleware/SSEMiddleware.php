<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class SSEMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->is('progress/stream/*')) {
            // Disable output buffering for SSE
            if (ob_get_level()) {
                ob_end_clean();
            }
            
            // Set execution time limit for long-running connections
            set_time_limit(0);
            
            // Ignore user abort to allow proper cleanup
            ignore_user_abort(true);
            
            // Log SSE connection
            Log::info('SSE connection established', [
                'task_id' => $request->route('taskId'),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
        }
        
        return $next($request);
    }
}
