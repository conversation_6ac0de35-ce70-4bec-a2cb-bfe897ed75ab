<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\ErrorMessages;
use App\Helpers\CrmExcelDate;
use Illuminate\Validation\Rule;

class AccountDoctorsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function store(){
        return [
            'doctor_id'  =>  ['required','integer','exists_not_soft_deleted:doctors,id'],
            'line_id'    =>  ['required','integer','exists_not_soft_deleted:lines,id'],
            'from_date'  =>  ['required','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'to_date'    =>  ['nullable','string','max:191','after_or_equal:from_date','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'class_id'   =>  ['nullable','integer','exists_not_soft_deleted:classes,id'],
        ];
    }
    public function update(){
        return [
            'account_id'      =>  ['required','integer','exists_not_soft_deleted:accounts,id'],
            'doctor_id'       =>  ['required','integer','exists_not_soft_deleted:doctors,id'],
            'line_id'         =>  ['required','integer','exists_not_soft_deleted:lines,id'],
            'from_date'       =>  ['required','string','max:191','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'to_date'         =>  ['nullable','string','max:191','after_or_equal:from_date','date_format:'.CrmExcelDate::OFFICIAL_FORMAT],
            'class_id'        =>  ['nullable','integer','exists_not_soft_deleted:classes,id'],
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
