<?php


namespace App\Services;

use App\Http\Controllers\ProgressController;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class ProgressService
{
    private const PROGRESS_PREFIX = 'progress:';
    private const ACTIVE_STREAMS_PREFIX = 'active_streams:';
    private const CLEANUP_BATCH_SIZE = 100;

    /**
     * Clean up expired progress data and stale streams
     */
    public function cleanup(): void
    {
        try {
            $this->cleanupExpiredProgress();
            $this->cleanupStaleStreams();
            Log::info('Progress cleanup completed successfully');
        } catch (Exception $e) {
            Log::error('Progress cleanup failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Clean up expired progress entries
     */
    private function cleanupExpiredProgress(): void
    {
        $pattern = self::PROGRESS_PREFIX . '*';
        $cursor = 0;
        $cleaned = 0;

        do {
            $result = Redis::scan($cursor, [
                'match' => $pattern,
                'count' => self::CLEANUP_BATCH_SIZE
            ]);

            $cursor = $result[0];
            $keys = $result[1];

            foreach ($keys as $key) {
                $ttl = Redis::ttl($key);
                if ($ttl === -1) { // No expiration set
                    Redis::expire($key, 3600); // Set 1 hour expiration
                    $cleaned++;
                }
            }
        } while ($cursor !== 0);

        Log::info('Cleaned up progress entries', ['count' => $cleaned]);
    }

    /**
     * Clean up stale stream connections
     */
    private function cleanupStaleStreams(): void
    {
        $pattern = self::ACTIVE_STREAMS_PREFIX . '*';
        $cursor = 0;
        $cleaned = 0;

        do {
            $result = Redis::scan($cursor, [
                'match' => $pattern,
                'count' => self::CLEANUP_BATCH_SIZE
            ]);

            $cursor = $result[0];
            $keys = $result[1];

            foreach ($keys as $key) {
                $streams = Redis::hgetall($key);
                $currentTime = time();

                foreach ($streams as $streamId => $lastActivity) {
                    // Remove streams inactive for more than 10 minutes
                    if ($currentTime - $lastActivity > 600) {
                        Redis::hdel($key, $streamId);
                        $cleaned++;
                    }
                }

                // Remove empty hash
                if (Redis::hlen($key) === 0) {
                    Redis::del($key);
                }
            }
        } while ($cursor !== 0);

        Log::info('Cleaned up stale streams', ['count' => $cleaned]);
    }

    /**
     * Get statistics about active progress tracking
     */
    public function getStatistics(): array
    {
        $progressCount = 0;
        $activeStreams = 0;

        // Count progress entries
        $cursor = 0;
        do {
            $result = Redis::scan($cursor, [
                'match' => self::PROGRESS_PREFIX . '*',
                'count' => 100
            ]);
            $cursor = $result[0];
            $progressCount += count($result[1]);
        } while ($cursor !== 0);

        // Count active streams
        $cursor = 0;
        do {
            $result = Redis::scan($cursor, [
                'match' => self::ACTIVE_STREAMS_PREFIX . '*',
                'count' => 100
            ]);
            $cursor = $result[0];
            foreach ($result[1] as $key) {
                $activeStreams += Redis::hlen($key);
            }
        } while ($cursor !== 0);

        return [
            'active_progress_tasks' => $progressCount,
            'active_streams' => $activeStreams,
            'memory_usage' => memory_get_usage(true),
            'redis_memory' => Redis::info('memory')['used_memory_human'] ?? 'N/A'
        ];
    }
}
