<?php

namespace Tests\Unit\Services\Reports\SalesIncentives;

use App\Brand;
use App\Line;
use App\LineDivision;
use App\Product;
use App\Services\Reports\SalesIncentives\IncentiveSalesViewReportService;
use App\Services\Sales\SalesIncentiveHolder;
use Illuminate\Support\Collection;
use Mockery;
use PHPUnit\Framework\TestCase;

class IncentiveSalesViewReportServiceTest extends TestCase
{
    private IncentiveSalesViewReportService $service;
    private SalesIncentiveHolder $mockSalesIncentiveHolder;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockSalesIncentiveHolder = Mockery::mock(SalesIncentiveHolder::class);
        $this->service = new IncentiveSalesViewReportService($this->mockSalesIncentiveHolder);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_group_products_by_brand_creates_hierarchical_structure()
    {
        // Arrange
        $sampleData = collect([
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product A',
                'p_w' => 10.5,
                'product_value' => 100.0,
                'brand' => 'Brand X',
                'sales_unit' => 50,
                'sales_value' => 500.0,
                'target_unit' => 40,
                'target_value' => 400.0,
                'achievement_unit' => '125.00%',
                'achievement_value' => '125.00%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
                'is_brand_summary' => false,
            ],
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product B',
                'p_w' => 15.5,
                'product_value' => 150.0,
                'brand' => 'Brand X',
                'sales_unit' => 30,
                'sales_value' => 300.0,
                'target_unit' => 25,
                'target_value' => 250.0,
                'achievement_unit' => '120.00%',
                'achievement_value' => '120.00%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
                'is_brand_summary' => false,
            ],
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product C',
                'p_w' => 8.0,
                'product_value' => 80.0,
                'brand' => '', // No brand
                'sales_unit' => 20,
                'sales_value' => 200.0,
                'target_unit' => 30,
                'target_value' => 300.0,
                'achievement_unit' => '66.67%',
                'achievement_value' => '66.67%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
                'is_brand_summary' => false,
            ],
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Total',
                'p_w' => '',
                'product_value' => 330.0,
                'brand' => '',
                'sales_unit' => 100,
                'sales_value' => 1000.0,
                'target_unit' => 95,
                'target_value' => 950.0,
                'achievement_unit' => '105.26%',
                'achievement_value' => '105.26%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => true,
                'is_brand_summary' => false,
            ],
        ]);

        // Act
        $result = $this->service->groupProductsByBrand($sampleData);

        // Assert
        $this->assertCount(6, $result); // 2 brand summaries + 3 individual products + 1 total

        // Check the structure: Brand X summary should come first
        $resultArray = $result->values()->toArray();

        // First should be Brand X summary
        $this->assertEquals('Brand X', $resultArray[0]['product']);
        $this->assertTrue($resultArray[0]['is_brand_summary']);
        $this->assertEquals(26.0, $resultArray[0]['p_w']); // 10.5 + 15.5
        $this->assertEquals(250.0, $resultArray[0]['product_value']); // 100.0 + 150.0
        $this->assertEquals(80, $resultArray[0]['sales_unit']); // 50 + 30
        $this->assertEquals(2, $resultArray[0]['product_count']);

        // Second should be Product A (individual)
        $this->assertEquals('Product A', $resultArray[1]['product']);
        $this->assertFalse($resultArray[1]['is_brand_summary']);
        $this->assertEquals('Brand X', $resultArray[1]['brand']);

        // Third should be Product B (individual)
        $this->assertEquals('Product B', $resultArray[2]['product']);
        $this->assertFalse($resultArray[2]['is_brand_summary']);
        $this->assertEquals('Brand X', $resultArray[2]['brand']);

        // Fourth should be No Brand summary
        $this->assertEquals('No Brand', $resultArray[3]['product']);
        $this->assertTrue($resultArray[3]['is_brand_summary']);
        $this->assertEquals('', $resultArray[3]['brand']);
        $this->assertEquals(1, $resultArray[3]['product_count']);

        // Fifth should be Product C (individual)
        $this->assertEquals('Product C', $resultArray[4]['product']);
        $this->assertFalse($resultArray[4]['is_brand_summary']);
        $this->assertEquals('', $resultArray[4]['brand']);

        // Last should be Total record
        $this->assertEquals('Total', $resultArray[5]['product']);
        $this->assertTrue($resultArray[5]['is_total']);
        $this->assertFalse($resultArray[5]['is_brand_summary']);
    }

    public function test_calculate_achievement_percentage_handles_zero_target()
    {
        // Arrange
        $sampleData = collect([
            [
                'id' => 1,
                'line' => 'Test Line',
                'division' => 'Test Division',
                'employee' => 'John Doe',
                'emp_code' => 'EMP001',
                'product' => 'Product A',
                'p_w' => 10.0,
                'product_value' => 100.0,
                'brand' => 'Brand X',
                'sales_unit' => 50,
                'sales_value' => 500.0,
                'target_unit' => 0, // Zero target
                'target_value' => 0, // Zero target
                'achievement_unit' => '0%',
                'achievement_value' => '0%',
                'color' => '#FF0000',
                'level' => 1,
                'is_total' => false,
                'is_brand_summary' => false,
            ],
        ]);

        // Act
        $result = $this->service->groupProductsByBrand($sampleData);

        // Assert
        $this->assertCount(2, $result); // 1 brand summary + 1 individual product

        // Check brand summary (first record)
        $brandSummary = $result->first();
        $this->assertTrue($brandSummary['is_brand_summary']);
        $this->assertEquals('0%', $brandSummary['achievement_unit']);
        $this->assertEquals('0%', $brandSummary['achievement_value']);

        // Check individual product (second record)
        $individualProduct = $result->skip(1)->first();
        $this->assertFalse($individualProduct['is_brand_summary']);
        $this->assertEquals('Product A', $individualProduct['product']);
    }
}
